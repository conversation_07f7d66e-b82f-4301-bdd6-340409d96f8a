from aiogram import Router, F
from aiogram.fsm.state import StatesGroup, State
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from common.microtopics.register_handlers import register_microtopics_handlers
from common.utils import check_if_id_in_callback_data
import logging

class TeacherAnalyticsStates(StatesGroup):
    main = State()
    select_group_for_student = State()
    select_student = State()
    student_stats = State()
    student_stats_display = State()  # Новое состояние для отображения статистики студента
    select_group_for_group = State()
    group_stats = State()
    group_stats_display = State()  # Новое состояние для отображения статистики группы
    select_subject = State()
    subject_stats = State()
    subject_stats_display = State()  # Новое состояние для отображения статистики предмета

    # Состояния для универсальных микротем
    detailed_microtopics = State()  # Детальная статистика по микротемам
    summary_microtopics = State()   # Сводка сильных/слабых тем

router = Router()

# Функция возврата из изображений микротем к статистике студента
async def back_from_microtopics_to_student_analytics(callback: CallbackQuery, state: FSMContext):
    """Возврат из состояний с картинками микротем к статистике студента"""
    try:
        # Получаем сохраненные данные из состояния
        data = await state.get_data()

        # Пытаемся получить student_id из разных источников
        student_id = None

        # 1. Из сохраненных данных состояния
        if 'selected_student_id' in data:
            student_id = data['selected_student_id']
        elif 'student_id' in data:
            student_id = data['student_id']

        # 2. Если не найден, пытаемся извлечь из последнего callback_data
        if not student_id and hasattr(callback, 'data'):
            # Проверяем, есть ли в callback_data информация о студенте
            if 'analytics_student_' in callback.data:
                try:
                    student_id = int(callback.data.replace('analytics_student_', ''))
                except ValueError:
                    pass

        if not student_id:
            # Если данных нет, удаляем сообщение с изображением и возвращаемся к выбору студентов
            try:
                await callback.message.delete()
            except Exception as delete_error:
                print(f"Не удалось удалить сообщение: {delete_error}")

            from common.analytics.handlers import select_student_for_analytics
            await select_student_for_analytics(callback, state, "teacher")
            await state.set_state(TeacherAnalyticsStates.select_student)
            return

        # Удаляем сообщение с изображением
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        # Получаем данные студента и формируем сообщение напрямую
        from database.repositories import StudentRepository
        from common.analytics.keyboards import get_student_microtopics_kb, get_back_to_analytics_kb

        student = await StudentRepository.get_by_id(int(student_id))
        if not student or not student.groups:
            await callback.message.answer(
                "❌ Студент не найден или не назначен в группу",
                reply_markup=get_back_to_analytics_kb()
            )
            return

        # Получаем общую статистику
        general_stats = await StudentRepository.get_general_stats(int(student_id))

        # Определяем целевую группу из сохраненных данных состояния
        selected_group_id = data.get('group')  # Используем 'group', а не 'selected_group'
        target_group = None

        if selected_group_id:
            # Ищем выбранную группу среди групп студента
            for group in student.groups:
                if str(group.id) == str(selected_group_id):
                    target_group = group
                    break

        # Если выбранная группа не найдена, берем первую группу
        if not target_group:
            target_group = student.groups[0]

        # Формируем базовую информацию
        group_names = [group.name for group in student.groups]
        result_text = f"👤 Студент: {student.user.name}\n"
        result_text += f"📚 Группы: {', '.join(group_names)}\n"
        result_text += f"💎 Тариф: {student.tariff or 'Не указан'}\n\n"
        result_text += f"📊 Общая статистика:\n"
        result_text += f"   • Баллы: {general_stats.get('total_points', 0)}\n"
        result_text += f"   • Уровень: {student.level}\n"
        result_text += f"   • Выполнено ДЗ: {general_stats.get('unique_completed', 0)} (всего попыток: {general_stats.get('total_completed', 0)})\n\n"
        result_text += f"📗 Предмет: {target_group.subject.name}\n"
        result_text += "Выберите, что хотите посмотреть:"

        # Отправляем новое сообщение
        await callback.message.answer(
            result_text,
            reply_markup=get_student_microtopics_kb(student.id, target_group.subject.id)
        )
        await state.set_state(TeacherAnalyticsStates.student_stats)

    except Exception as e:
        print(f"Ошибка при возврате из микротем: {e}")
        # Удаляем сообщение с изображением и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        from common.analytics.keyboards import get_analytics_menu_kb
        await callback.message.answer(
            "📊 Аналитика\n\nВыберите тип статистики:",
            reply_markup=get_analytics_menu_kb("teacher")
        )
        await state.set_state(TeacherAnalyticsStates.main)

# Регистрируем общие обработчики аналитики для преподавателя
from common.analytics.register_handlers import register_analytics_handlers
register_analytics_handlers(router, TeacherAnalyticsStates, "teacher")

from common.statistics import show_student_analytics, show_group_analytics

# Настройка логирования
logging.basicConfig(level=logging.INFO)



# Регистрируем универсальные обработчики микротем для преподавателя
register_microtopics_handlers(
    router=router,
    states_group=TeacherAnalyticsStates,
    role="teacher",
    detailed_callback_prefix="teacher_microtopics_page",
    summary_callback_prefix="teacher_summary_page",
    detailed_state=TeacherAnalyticsStates.detailed_microtopics,
    summary_state=TeacherAnalyticsStates.summary_microtopics,
    subject_details_state=TeacherAnalyticsStates.student_stats,
    back_keyboard_func=lambda: None,
    back_from_image_func=back_from_microtopics_to_student_analytics,
    title="📊 Статистика студента\n📈 % понимания по микротемам",
    items_per_page_detailed=15,
    items_per_page_summary=15,
    premium_check=False  # Преподаватель имеет доступ ко всем функциям
)

# Обработчики пагинации для групп
@router.callback_query(TeacherAnalyticsStates.group_stats_display, F.data.startswith("teacher_group_microtopics_page_"))
async def teacher_handle_group_microtopics_pagination(callback: CallbackQuery, state: FSMContext):
    """Обработчик пагинации микротем группы для преподавателя"""
    from common.microtopics.handlers import handle_microtopics_pagination_universal
    await handle_microtopics_pagination_universal(
        callback=callback,
        state=state,
        callback_prefix="teacher_group_microtopics_page",
        display_mode="detailed",
        role="teacher"
    )

# Обработчик возврата из изображений микротем группы
@router.callback_query(TeacherAnalyticsStates.group_stats_display, F.data == "back_from_microtopics_image")
async def teacher_back_from_group_microtopics_image(callback: CallbackQuery, state: FSMContext):
    """Возврат из изображений микротем группы к статистике группы для преподавателя"""
    from common.microtopics.handlers import back_from_group_microtopics_to_analytics
    await back_from_group_microtopics_to_analytics(callback, state, "teacher")

# Обработчик возврата из изображений микротем группы
@router.callback_query(TeacherAnalyticsStates.group_stats_display, F.data == "back_from_microtopics_image")
async def teacher_back_from_group_microtopics_image(callback: CallbackQuery, state: FSMContext):
    """Возврат из изображений микротем группы к статистике группы для преподавателя"""
    from common.microtopics.handlers import back_from_group_microtopics_to_analytics
    await back_from_group_microtopics_to_analytics(callback, state, "teacher")