#!/usr/bin/env python3
"""
Тест специфичного callback для куратора
"""
import asyncio
import logging

async def test_curator_specific_callback():
    """Тест специфичного callback для куратора"""
    print("🧪 Тестирование специфичного callback для куратора")
    
    try:
        # Проверяем, что новые обработчики добавлены
        from curator.states.states_tests import CuratorTestsStatisticsStates
        
        detailed_state = CuratorTestsStatisticsStates.month_entry_detailed_microtopics
        summary_state = CuratorTestsStatisticsStates.month_entry_summary_microtopics
        
        print(f"✅ Состояния куратора найдены:")
        print(f"   - Detailed: {detailed_state}")
        print(f"   - Summary: {summary_state}")
        
        # Проверяем функцию клавиатуры
        from common.microtopics.keyboards import get_microtopics_pagination_kb
        
        # Тестируем с callback для куратора
        kb_curator = get_microtopics_pagination_kb(
            current_page=0,
            total_items=10,
            items_per_page=5,
            callback_prefix="curator_month_entry_page",
            back_keyboard_func=None,
            back_callback_data="curator_back_to_month_entry_result"
        )
        
        # Проверяем, что кнопка "Назад" использует правильный callback
        back_button_found = False
        for row in kb_curator.inline_keyboard:
            for button in row:
                if button.text == "⬅️ Назад" and button.callback_data == "curator_back_to_month_entry_result":
                    back_button_found = True
                    break
        
        if back_button_found:
            print("✅ Кнопка 'Назад' использует специфичный callback для куратора")
        else:
            print("❌ Кнопка 'Назад' не использует специфичный callback")
            return False
        
        # Тестируем с обычным callback
        kb_student = get_microtopics_pagination_kb(
            current_page=0,
            total_items=10,
            items_per_page=5,
            callback_prefix="student_month_entry_page",
            back_keyboard_func=None,
            back_callback_data="back_from_microtopics_image"
        )
        
        # Проверяем, что кнопка "Назад" использует обычный callback
        back_button_found = False
        for row in kb_student.inline_keyboard:
            for button in row:
                if button.text == "⬅️ Назад" and button.callback_data == "back_from_microtopics_image":
                    back_button_found = True
                    break
        
        if back_button_found:
            print("✅ Кнопка 'Назад' использует обычный callback для студента")
        else:
            print("❌ Кнопка 'Назад' не использует обычный callback для студента")
            return False
        
        print("\n🎉 Все проверки прошли успешно!")
        print("\n📋 Резюме исправлений:")
        print("1. ✅ Добавлены специфичные обработчики для callback 'curator_back_to_month_entry_result'")
        print("2. ✅ Функция get_microtopics_pagination_kb использует параметр back_callback_data")
        print("3. ✅ Универсальная функция определяет callback в зависимости от префикса")
        print("4. ✅ Куратор использует специфичный callback, студент - обычный")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов специфичного callback для куратора\n")
    
    success = await test_curator_specific_callback()
    
    if success:
        print("\n✅ Исправление готово!")
        print("\n🔧 Что было исправлено:")
        print("- Проблема: кнопка 'Назад' из микротем не возвращала к правильному экрану")
        print("- Решение: добавлен специфичный callback 'curator_back_to_month_entry_result'")
        print("- Логика: куратор использует специфичный callback, остальные - универсальный")
        print("\n🧪 Теперь кнопка 'Назад' должна возвращать к результату теста!")
    else:
        print("\n❌ Тесты не прошли, требуется дополнительная отладка")

if __name__ == "__main__":
    asyncio.run(main())
