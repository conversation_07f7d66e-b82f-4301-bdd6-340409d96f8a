#!/usr/bin/env python3
"""
Тест исправления навигации для кнопки "Назад" из изображений микротем
"""
import asyncio
import logging
from unittest.mock import MagicMock, AsyncMock

async def test_navigation_fix():
    """Тест исправления навигации"""
    print("🧪 Тестирование исправления навигации для кнопки 'Назад' из микротем")
    
    try:
        # Проверяем импорты
        from curator.states.states_tests import CuratorTestsStatisticsStates, STATE_TRANSITIONS, STATE_HANDLERS
        from common.handlers import back_from_microtopics_image_universal
        from common.navigation import NavigationManager
        
        print("✅ Все импорты успешны")
        
        # Проверяем состояния микротем
        detailed_state = CuratorTestsStatisticsStates.month_entry_detailed_microtopics
        summary_state = CuratorTestsStatisticsStates.month_entry_summary_microtopics
        result_state = CuratorTestsStatisticsStates.month_entry_result
        
        print(f"✅ Состояния найдены:")
        print(f"   - Detailed: {detailed_state}")
        print(f"   - Summary: {summary_state}")
        print(f"   - Result: {result_state}")
        
        # Проверяем переходы
        assert detailed_state in STATE_TRANSITIONS, "Переход для detailed состояния не найден"
        assert summary_state in STATE_TRANSITIONS, "Переход для summary состояния не найден"
        
        detailed_transition = STATE_TRANSITIONS[detailed_state]
        summary_transition = STATE_TRANSITIONS[summary_state]
        
        assert detailed_transition == result_state, f"Неверный переход для detailed: {detailed_transition} != {result_state}"
        assert summary_transition == result_state, f"Неверный переход для summary: {summary_transition} != {result_state}"
        
        print("✅ Переходы настроены корректно:")
        print(f"   - {detailed_state} -> {detailed_transition}")
        print(f"   - {summary_state} -> {summary_transition}")
        
        # Проверяем обработчики
        assert detailed_state in STATE_HANDLERS, "Обработчик для detailed состояния не найден"
        assert summary_state in STATE_HANDLERS, "Обработчик для summary состояния не найден"
        
        print("✅ Обработчики зарегистрированы")
        
        # Тестируем универсальный обработчик
        callback = MagicMock()
        callback.from_user = MagicMock()
        callback.from_user.id = 955518340
        callback.data = "back_from_microtopics_image"
        
        state = MagicMock()
        state.get_state = AsyncMock(return_value="CuratorTestsStatisticsStates:month_entry_detailed_microtopics")
        
        # Мокаем navigation_manager
        from unittest.mock import patch
        with patch('common.handlers.navigation_manager') as mock_nav:
            mock_nav.handle_back = AsyncMock()
            
            await back_from_microtopics_image_universal(callback, state, "curator")
            
            # Проверяем, что navigation_manager.handle_back был вызван
            mock_nav.handle_back.assert_called_once_with(callback, state, "curator")
            
        print("✅ Универсальный обработчик работает корректно")
        
        print("\n🎉 Все тесты прошли успешно!")
        print("\n📋 Резюме исправлений:")
        print("1. ✅ Добавлен универсальный обработчик back_from_microtopics_image_universal в common/handlers.py")
        print("2. ✅ Добавлены переходы для состояний микротем в get_transitions_handlers")
        print("3. ✅ Добавлены обработчики для состояний микротем в get_transitions_handlers")
        print("4. ✅ Система навигации теперь корректно обрабатывает возврат из микротем")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов исправления навигации\n")
    
    success = await test_navigation_fix()
    
    if success:
        print("\n✅ Исправление готово к тестированию!")
        print("\n🔧 Что было исправлено:")
        print("- Проблема: обработчик 'back_from_microtopics_image' не обрабатывался (is not handled)")
        print("- Причина: отсутствовал универсальный обработчик и переходы для состояний микротем")
        print("- Решение: добавлен универсальный обработчик + настроена система навигации")
        print("\n🧪 Теперь кнопка 'Назад' из микротем должна работать для всех ролей!")
    else:
        print("\n❌ Тесты не прошли, требуется дополнительная отладка")

if __name__ == "__main__":
    asyncio.run(main())
