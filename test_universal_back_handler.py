#!/usr/bin/env python3
"""
Тест универсального обработчика кнопки "Назад" для всех ролей
"""

import asyncio
import logging
from unittest.mock import AsyncMock, MagicMock

async def test_universal_back_handler():
    """Тестируем универсальный обработчик для всех ролей"""
    
    print("🔍 Тестируем универсальный обработчик кнопки 'Назад'...")
    
    try:
        # Импортируем состояния всех ролей
        from curator.states.states_tests import CuratorTestsStatisticsStates
        from teacher.states.states_tests import TeacherTestsStatisticsStates  
        from manager.states.states_tests import ManagerTestsStatisticsStates
        
        print("✅ Все классы состояний импортированы")
        
        # Проверяем, что универсальный обработчик зарегистрирован
        from common.microtopics.register_handlers import register_month_entry_test_microtopics_handlers
        from aiogram import Router
        
        # Создаем тестовый роутер
        test_router = Router()
        
        # Создаем заглушку для back_keyboard_func
        def dummy_back_keyboard():
            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
            return InlineKeyboardMarkup(inline_keyboard=[[
                InlineKeyboardButton(text="⬅️ Назад", callback_data="back")
            ]])

        # Регистрируем обработчики для куратора
        register_month_entry_test_microtopics_handlers(
            router=test_router,
            states_group=CuratorTestsStatisticsStates,
            detailed_callback_prefix="curator_month_entry_page",
            summary_callback_prefix="curator_month_entry_summary_page",
            detailed_state=CuratorTestsStatisticsStates.month_entry_detailed_microtopics,
            summary_state=CuratorTestsStatisticsStates.month_entry_summary_microtopics,
            result_state=CuratorTestsStatisticsStates.month_entry_result,
            back_keyboard_func=dummy_back_keyboard
        )

        print("✅ Обработчики куратора зарегистрированы")

        # Регистрируем обработчики для учителя
        register_month_entry_test_microtopics_handlers(
            router=test_router,
            states_group=TeacherTestsStatisticsStates,
            detailed_callback_prefix="teacher_month_entry_page",
            summary_callback_prefix="teacher_month_entry_summary_page",
            detailed_state=TeacherTestsStatisticsStates.month_entry_detailed_microtopics,
            summary_state=TeacherTestsStatisticsStates.month_entry_summary_microtopics,
            result_state=TeacherTestsStatisticsStates.month_entry_result,
            back_keyboard_func=dummy_back_keyboard
        )

        print("✅ Обработчики учителя зарегистрированы")

        # Регистрируем обработчики для менеджера
        register_month_entry_test_microtopics_handlers(
            router=test_router,
            states_group=ManagerTestsStatisticsStates,
            detailed_callback_prefix="manager_month_entry_page",
            summary_callback_prefix="manager_month_entry_summary_page",
            detailed_state=ManagerTestsStatisticsStates.month_entry_detailed_microtopics,
            summary_state=ManagerTestsStatisticsStates.month_entry_summary_microtopics,
            result_state=ManagerTestsStatisticsStates.month_entry_result,
            back_keyboard_func=dummy_back_keyboard
        )

        print("✅ Обработчики менеджера зарегистрированы")
        
        # Проверяем количество зарегистрированных обработчиков
        handlers_count = len(test_router.callback_query.handlers)
        print(f"✅ Всего зарегистрировано обработчиков: {handlers_count}")
        
        # Проверяем, что есть обработчик для "back_to_month_entry_result"
        back_handler_found = False
        universal_handler_name = None

        for handler in test_router.callback_query.handlers:
            # Проверяем название функции
            if "universal_back_to_month_entry_result" in handler.callback.__name__:
                back_handler_found = True
                universal_handler_name = handler.callback.__name__
                print(f"✅ Найден универсальный обработчик: {universal_handler_name}")
                break

            # Также проверяем фильтры обработчика
            if hasattr(handler, 'filters') and handler.filters:
                for filter_obj in handler.filters:
                    if hasattr(filter_obj, 'data') and filter_obj.data == "back_to_month_entry_result":
                        back_handler_found = True
                        universal_handler_name = handler.callback.__name__
                        print(f"✅ Найден универсальный обработчик по фильтру: {universal_handler_name}")
                        break
        
        if back_handler_found:
            print("✅ Универсальный обработчик 'back_to_month_entry_result' найден!")
        else:
            print("❌ Универсальный обработчик 'back_to_month_entry_result' НЕ найден!")
            
            # Выводим все найденные обработчики для отладки
            print("\n🔍 Все зарегистрированные обработчики:")
            for i, handler in enumerate(test_router.callback_query.handlers):
                print(f"  {i+1}. {handler.callback.__name__}")
                if hasattr(handler, 'filters') and handler.filters:
                    for filter_obj in handler.filters:
                        if hasattr(filter_obj, 'data'):
                            print(f"     - callback_data: {filter_obj.data}")
        
        # Тестируем логику определения роли
        test_states = [
            ("CuratorTestsStatisticsStates:month_entry_detailed_microtopics", "curator"),
            ("TeacherTestsStatisticsStates:month_entry_summary_microtopics", "teacher"),
            ("ManagerTestsStatisticsStates:month_entry_detailed_microtopics", "manager"),
            ("StudentTestsStates:some_state", None),  # Не поддерживаемая роль
        ]
        
        print("\n🔍 Тестируем логику определения роли:")
        for state_str, expected_role in test_states:
            if state_str.startswith('CuratorTestsStatisticsStates:'):
                detected_role = "curator"
            elif state_str.startswith('TeacherTestsStatisticsStates:'):
                detected_role = "teacher"
            elif state_str.startswith('ManagerTestsStatisticsStates:'):
                detected_role = "manager"
            else:
                detected_role = None
                
            if detected_role == expected_role:
                print(f"  ✅ {state_str} -> {detected_role}")
            else:
                print(f"  ❌ {state_str} -> {detected_role} (ожидалось: {expected_role})")
        
        print("\n🎉 ТЕСТ ЗАВЕРШЕН!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в тесте: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_universal_back_handler())
