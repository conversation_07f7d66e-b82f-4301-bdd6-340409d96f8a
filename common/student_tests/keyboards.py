from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from typing import List

from common.keyboards import get_main_menu_back_button, back_to_main_button


async def get_test_subjects_kb(test_type: str, user_id: int = None) -> InlineKeyboardMarkup:
    """
    Клавиатура с предметами для тестов

    Args:
        test_type: Тип теста (course_entry, month_entry, month_control)
        user_id: ID пользователя для фильтрации предметов
    """
    # Для входного теста курса всегда показываем ВСЕ предметы из БД
    # независимо от того, есть ли у пользователя назначенные курсы
    if test_type == "course_entry":
        try:
            from database import SubjectRepository
            subjects_db = await SubjectRepository.get_all()
            subjects = []
            for subject_db in subjects_db:
                subjects.append({
                    "id": str(subject_db.id),
                    "name": subject_db.name
                })
        except Exception as e:
            print(f"Ошибка при получении всех предметов для входного теста курса: {e}")
            subjects = []
    elif user_id:
        # Для остальных тестов получаем предметы студента из базы данных
        try:
            from database import SubjectRepository
            subjects_db = await SubjectRepository.get_subjects_by_user_courses_and_groups(user_id)

            subjects = []
            for subject_db in subjects_db:
                # Используем реальный ID предмета из БД
                subjects.append({
                    "id": str(subject_db.id),  # Используем реальный ID
                    "name": subject_db.name
                })

        except Exception as e:
            print(f"Ошибка при получении предметов студента: {e}")
            # Fallback к пустому списку
            subjects = []
    else:
        # Получаем все предметы из БД (для обратной совместимости)
        try:
            from database import SubjectRepository
            subjects_db = await SubjectRepository.get_all()
            subjects = []
            for subject_db in subjects_db:
                subjects.append({
                    "id": str(subject_db.id),
                    "name": subject_db.name
                })
        except Exception as e:
            print(f"Ошибка при получении всех предметов: {e}")
            subjects = []

    buttons = []
    for subject in subjects:
        buttons.append([
            InlineKeyboardButton(
                text=subject["name"],
                callback_data=f"{test_type}_sub_{subject['id']}"
            )
        ])
    
    # Добавляем кнопку "Назад"
    buttons.extend(get_main_menu_back_button())
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_month_test_kb(test_type: str, subject_id: str, user_id: int = None) -> InlineKeyboardMarkup:
    """
    Клавиатура с доступными тестами месяца для предмета

    Args:
        test_type: Тип теста (month_entry, month_control)
        subject_id: ID предмета (строковый, например "math", "chem")
        user_id: ID пользователя для получения его курсов
    """
    buttons = []

    if user_id:
        try:
            from database.repositories.user_repository import UserRepository
            from database.repositories.student_repository import StudentRepository
            from database.repositories.subject_repository import SubjectRepository
            from database.repositories.month_test_repository import MonthTestRepository

            # Получаем пользователя и студента
            user = await UserRepository.get_by_telegram_id(user_id)
            if user:
                student = await StudentRepository.get_by_user_id(user.id)
                if student:
                    # Получаем предмет по ID (теперь subject_id - это реальный ID из БД)
                    try:
                        subject_id_int = int(subject_id)
                        subject = await SubjectRepository.get_by_id(subject_id_int)
                    except (ValueError, TypeError):
                        # Если subject_id не число, пытаемся найти по имени
                        subject = await SubjectRepository.get_by_name(subject_id)

                    if subject:
                        # Получаем курсы студента
                        from database.repositories.course_repository import CourseRepository
                        student_courses = await CourseRepository.get_by_student(student.id)

                        # Ищем тесты месяца для данного предмета в курсах студента
                        available_tests = []
                        for course in student_courses:
                            tests = await MonthTestRepository.get_by_course_subject(course.id, subject.id)
                            # Показываем все тесты для обоих режимов (входной и контрольный)
                            available_tests.extend(tests)

                        # Создаем кнопки для доступных тестов
                        for test in available_tests:
                            buttons.append([
                                InlineKeyboardButton(
                                    text=test.name,
                                    callback_data=f"{test_type}_{subject_id}_test_{test.id}"
                                )
                            ])

        except Exception as e:
            print(f"Ошибка при получении тестов месяца: {e}")

    # Если нет доступных тестов, показываем сообщение
    if not buttons:
        buttons.append([
            InlineKeyboardButton(
                text="📝 Нет доступных тестов",
                callback_data="no_tests_available"
            )
        ])

    # Добавляем кнопку "Назад"
    # back_action = "back_to_month_entry_subjects" if test_type == "month_entry" else "back_to_month_control_subjects"
    buttons.extend(
        get_main_menu_back_button()
    )

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def get_back_to_test_kb() -> InlineKeyboardMarkup:
    """Клавиатура для возврата в меню тестов"""
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button()
    ]
    )

def get_tests_menu_kb() -> InlineKeyboardMarkup:
    """
    Клавиатура главного меню тестов (устаревшая функция)
    Рекомендуется использовать get_tests_menu_by_tariff() для учета статуса регистрации
    """
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📝 Входной тест курса", callback_data="course_entry_test")],
        [InlineKeyboardButton(text="📊 Входной тест месяца", callback_data="month_entry_test")],
        [InlineKeyboardButton(text="📈 Контрольный тест месяца", callback_data="month_control_test")],
        *get_main_menu_back_button()
    ])


async def get_tests_menu_by_tariff(telegram_id: int) -> InlineKeyboardMarkup:
    """
    Получить клавиатуру меню тестов с учетом статуса регистрации пользователя

    Args:
        telegram_id: Telegram ID пользователя

    Returns:
        Клавиатура с тестами в зависимости от статуса пользователя
    """
    # Проверяем, зарегистрирован ли пользователь в системе
    is_registered = await _is_user_registered(telegram_id)

    buttons = []

    # Входной тест курса показываем ТОЛЬКО незарегистрированным пользователям
    if not is_registered:
        buttons.append([InlineKeyboardButton(text="📝 Входной тест курса", callback_data="course_entry_test")])

    # Остальные тесты показываем всем (ограничения проверяются в обработчиках)
    buttons.extend([
        [InlineKeyboardButton(text="📊 Входной тест месяца", callback_data="month_entry_test")],
        [InlineKeyboardButton(text="📈 Контрольный тест месяца", callback_data="month_control_test")]
    ])

    # Добавляем кнопку назад
    buttons.extend(get_main_menu_back_button())

    return InlineKeyboardMarkup(inline_keyboard=buttons)


async def _is_user_registered(telegram_id: int) -> bool:
    """
    Проверить, является ли пользователь зарегистрированным

    Кнопка "Входной тест курса" показывается только пользователям с ролью new_user

    Args:
        telegram_id: Telegram ID пользователя

    Returns:
        True если пользователь зарегистрирован (НЕ new_user), False если new_user
    """
    try:
        # Импортируем глобальный кэш ролей из middleware
        from middlewares.role_middleware import _global_role_cache

        # Проверяем, есть ли пользователь в любой из ролей
        for role_name, user_ids in _global_role_cache.items():
            if telegram_id in user_ids:
                # Пользователь найден в кэше - он зарегистрирован
                return True

        # Пользователь не найден в кэше - он new_user (незарегистрированный)
        return False

    except Exception as e:
        # В случае ошибки считаем пользователя зарегистрированным (не показываем входной тест)
        print(f"Ошибка при проверке роли пользователя {telegram_id}: {e}")
        return True