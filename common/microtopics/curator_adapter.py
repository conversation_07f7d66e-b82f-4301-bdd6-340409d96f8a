"""
Адаптер для интеграции системы отображения микротем куратора с универсальной системой изображений
"""
import logging
from typing import Optional
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
from .handlers import show_month_entry_test_microtopics_universal
from common.tests_statistics.keyboards import get_back_kb

logger = logging.getLogger(__name__)


async def convert_curator_callback_to_student_format(
    group_id: int, 
    month_test_id: int, 
    student_id: int
) -> Optional[int]:
    """
    Конвертировать формат callback_data куратора в test_result_id для использования 
    в универсальной системе отображения микротем студента
    
    Args:
        group_id: ID группы (из callback_data куратора)
        month_test_id: ID теста месяца
        student_id: ID студента
        
    Returns:
        test_result_id для использования в системе студента или None если не найден
    """
    try:
        test_result = await MonthEntryTestResultRepository.get_by_student_and_month_test(
            student_id, month_test_id
        )
        return test_result.id if test_result else None
    except Exception as e:
        logger.error(f"Ошибка при конвертации формата куратора: {e}")
        return None


async def show_curator_month_entry_microtopics_with_images(
    callback: CallbackQuery,
    state: FSMContext,
    group_id: int,
    month_test_id: int, 
    student_id: int,
    target_state,
    display_mode: str = "detailed"
):
    """
    Показать микротемы входного теста месяца куратору с использованием 
    универсальной системы изображений студента
    
    Args:
        callback: Callback query
        state: FSM context
        group_id: ID группы
        month_test_id: ID теста месяца
        student_id: ID студента
        target_state: Целевое состояние для установки
        display_mode: Режим отображения ("detailed" или "summary")
    """
    try:
        # Конвертируем формат куратора в формат студента
        test_result_id = await convert_curator_callback_to_student_format(
            group_id, month_test_id, student_id
        )
        
        if not test_result_id:
            await callback.message.edit_text(
                "❌ Результат теста не найден",
                reply_markup=get_back_kb()
            )
            return
        
        # Сохраняем данные куратора для навигации назад
        await state.update_data(
            month_entry_group_id=group_id,
            month_entry_month_test_id=month_test_id,
            month_entry_student_id=student_id,
            month_entry_test_result_id=test_result_id  # Для совместимости с системой студента
        )
        
        # Определяем callback_prefix и caption в зависимости от режима
        if display_mode == "detailed":
            callback_prefix = "curator_month_entry_page"
            caption = "📊 Детальная статистика входного теста месяца"
        else:
            callback_prefix = "curator_month_entry_summary_page"
            caption = "💪 Сводка по входному тесту месяца"
        
        # Логируем установку состояния
        logger.info(f"🔧 АДАПТЕР: Устанавливаем состояние {target_state} для пользователя {callback.from_user.id}")

        # Используем универсальную систему отображения микротем студента
        await show_month_entry_test_microtopics_universal(
            callback=callback,
            state=state,
            test_result_id=test_result_id,
            target_state=target_state,
            callback_prefix=callback_prefix,
            back_keyboard_func=get_curator_back_to_result_kb,
            display_mode=display_mode,
            items_per_page=15,
            caption=caption,
            premium_check=False  # Куратор имеет доступ к статистике
        )

        # Проверяем, какое состояние установилось
        final_state = await state.get_state()
        logger.info(f"🔧 АДАПТЕР: Финальное состояние после отображения: {final_state}")

        # Добавляем задержку и проверяем состояние еще раз
        import asyncio
        await asyncio.sleep(0.1)
        final_state_after_delay = await state.get_state()
        logger.error(f"🔧 АДАПТЕР: Состояние через 0.1 сек: {final_state_after_delay}")

        if final_state != final_state_after_delay:
            logger.error(f"🚨 СОСТОЯНИЕ ИЗМЕНИЛОСЬ! {final_state} → {final_state_after_delay}")
        
    except Exception as e:
        logger.error(f"Ошибка при отображении микротем куратору: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении статистики микротем",
            reply_markup=get_back_kb()
        )


def get_curator_back_to_result_kb():
    """Клавиатура возврата для куратора (используем стандартную)"""
    return get_back_kb()


async def curator_back_from_month_entry_microtopics_to_result(callback: CallbackQuery, state: FSMContext):
    """
    Универсальная функция возврата куратора из изображений микротем входного теста к результату теста
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('month_entry_group_id')
        month_test_id = data.get('month_entry_month_test_id')
        student_id = data.get('month_entry_student_id')
        
        if not all([group_id, month_test_id, student_id]):
            logger.error("Не найдены данные для возврата к результату теста куратора")
            try:
                # Удаляем сообщение с изображением и отправляем новое
                await callback.message.delete()
                await callback.message.answer(
                    "❌ Ошибка навигации",
                    reply_markup=get_back_kb()
                )
            except Exception:
                # Если не удалось удалить, просто отвечаем на callback
                await callback.answer("❌ Ошибка навигации")
            return
        
        # Возвращаемся к результату через существующую функцию куратора
        from common.tests_statistics.handlers import show_month_entry_student_detail
        await show_month_entry_student_detail(callback, state, group_id, month_test_id, student_id)
        
        # Устанавливаем правильное состояние
        from curator.states.states_tests import CuratorTestsStatisticsStates
        await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
        
    except Exception as e:
        logger.error(f"Ошибка при возврате к результату теста куратора: {e}")
        try:
            # Удаляем сообщение с изображением и отправляем новое
            await callback.message.delete()
            await callback.message.answer(
                "❌ Ошибка при возврате к результату",
                reply_markup=get_back_kb()
            )
        except Exception:
            # Если не удалось удалить, просто отвечаем на callback
            await callback.answer("❌ Ошибка при возврате к результату")
