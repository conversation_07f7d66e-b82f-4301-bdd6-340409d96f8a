from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
import math
import logging

logger = logging.getLogger(__name__)


def get_microtopics_pagination_kb(
    current_page: int,
    total_items: int,
    items_per_page: int,
    callback_prefix: str,
    back_keyboard_func,
    back_callback_data: str = "back_from_microtopics_image"
) -> InlineKeyboardMarkup:
    """
    Универсальная клавиатура пагинации для микротем
    
    Args:
        current_page: Текущая страница (0-based)
        total_items: Общее количество элементов
        items_per_page: Элементов на страницу
        callback_prefix: Префикс для callback (например: "student_microtopics_page")
        back_keyboard_func: Функция для генерации кнопки "Назад"
    
    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками пагинации
    """
    # Вычисляем общее количество страниц
    total_pages = math.ceil(total_items / items_per_page)
    
    buttons = []
    
    # Если больше одной страницы, добавляем кнопки пагинации
    if total_pages > 1:
        pagination_row = []
        
        # Кнопка "Предыдущая страница"
        if current_page > 0:
            pagination_row.append(
                InlineKeyboardButton(
                    text="◀️",
                    callback_data=f"{callback_prefix}_{current_page - 1}"
                )
            )
        
        # Индикатор текущей страницы
        pagination_row.append(
            InlineKeyboardButton(
                text=f"{current_page + 1}/{total_pages}",
                callback_data="page_indicator"  # Неактивная кнопка
            )
        )
        
        # Кнопка "Следующая страница"
        if current_page < total_pages - 1:
            pagination_row.append(
                InlineKeyboardButton(
                    text="▶️",
                    callback_data=f"{callback_prefix}_{current_page + 1}"
                )
            )
        
        buttons.append(pagination_row)
    
    # Добавляем специальную кнопку "Назад" для состояний с изображениями

    buttons.append([
        InlineKeyboardButton(
            text="⬅️ Назад",
            callback_data=back_callback_data
        )
    ])

    # Добавляем только кнопку "Главное меню" (без дублирования "Назад")
    buttons.append([
        InlineKeyboardButton(
            text="🏠 Главное меню",
            callback_data="back_to_main"
        )
    ])
    
    return InlineKeyboardMarkup(inline_keyboard=buttons)
