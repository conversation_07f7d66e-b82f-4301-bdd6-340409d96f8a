from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.filters import StateFilter
from aiogram.fsm.context import FSMContext

from manager.keyboards.topics import (
    get_subjects_kb,
    get_topics_list_kb,
    get_topics_list_with_pagination_kb,
    TopicCallback,
    TopicActions
)
from manager.keyboards.main import get_manager_main_menu_kb
from aiogram.fsm.state import State, StatesGroup
from database import SubjectRepository, MicrotopicRepository
from common.keyboards import get_home_kb

class ManagerTopicStates(StatesGroup):
    main = State()  # Главное меню микротем (выбор предмета)
    topics_list = State()  # Список микротем предмета
    adding_topic = State()  # Добавление новой микротемы
    delete_multiple = State()  # Удаление нескольких микротем по номерам

router = Router()

@router.callback_query(F.data == "manager_topics")
async def show_subjects(callback: CallbackQuery, state: FSMContext):
    """Показываем список предметов"""
    await state.set_state(ManagerTopicStates.main)
    await callback.message.edit_text(
        text="Выберите предмет для работы с микротемами:",
        reply_markup=await get_subjects_kb()
    )

@router.callback_query(StateFilter(ManagerTopicStates.main), TopicCallback.filter(F.action == TopicActions.VIEW))
async def show_topics(callback: CallbackQuery, callback_data: TopicCallback, state: FSMContext):
    """Показываем список микротем для выбранного предмета"""
    subject_id = int(callback_data.subject)

    # Получаем предмет и его микротемы из базы данных
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_manager_main_menu_kb()
        )
        return

    microtopics = await MicrotopicRepository.get_by_subject(subject_id)

    await state.set_state(ManagerTopicStates.topics_list)
    await state.update_data(current_subject_id=subject_id, current_subject_name=subject.name)

    await callback.message.edit_text(
        text=f"📝 Микротемы по предмету {subject.name}:\n"
             f"Всего микротем: {len(microtopics)}",
        reply_markup=await get_topics_list_kb(subject.name, microtopics)
    )

@router.callback_query(StateFilter(ManagerTopicStates.topics_list), TopicCallback.filter(F.action == TopicActions.ADD))
async def start_add_topic(callback: CallbackQuery, callback_data: TopicCallback, state: FSMContext):
    """Начинаем процесс добавления микротемы"""
    subject_id = int(callback_data.subject)

    # Получаем название предмета для отображения
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_manager_main_menu_kb()
        )
        return

    await state.set_state(ManagerTopicStates.adding_topic)
    await state.update_data(subject_id=subject_id, subject_name=subject.name)

    await callback.message.edit_text(
        text=f"Предмет: {subject.name}\n\n"
             f"📝 Введите названия микротем:\n"
             f"• Одну микротему на строку\n"
             f"• Можно ввести до 200 микротем за раз\n"
             f"• Пустые строки будут пропущены\n\n"
             f"Пример:\n"
             f"Алканы\n"
             f"Алкены\n"
             f"Алкины",
        reply_markup=get_home_kb()
    )

@router.message(StateFilter(ManagerTopicStates.adding_topic))
async def process_topic_name(message: Message, state: FSMContext):
    """Обрабатываем ввод названий микротем (одну или несколько построчно)"""
    data = await state.get_data()
    subject_id = data['subject_id']
    subject_name = data['subject_name']

    # Разбиваем текст на строки и очищаем от пустых
    lines = [line.strip() for line in message.text.split('\n') if line.strip()]

    if not lines:
        await message.answer(
            text="❌ Введите хотя бы одно название микротемы:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем лимит (примерно 200 строк в пределах 4096 символов)
    if len(lines) > 200:
        await message.answer(
            text=f"❌ Слишком много микротем за раз (максимум 200, введено {len(lines)}).\n"
                 f"Разделите на несколько сообщений:",
            reply_markup=get_home_kb()
        )
        return

    try:
        if len(lines) == 1:
            # Одна микротема - используем старый метод
            microtopic = await MicrotopicRepository.create(lines[0], subject_id)
            created_count = 1
            result_text = f"✅ Микротема добавлена в предмет {subject_name}:\n{microtopic.number}. {microtopic.name}"
        else:
            # Несколько микротем - используем массовое создание
            created_microtopics = await MicrotopicRepository.create_multiple(lines, subject_id)
            created_count = len(created_microtopics)

            if created_count == len(lines):
                result_text = f"✅ Добавлено {created_count} микротем в предмет {subject_name}:\n"
                for mt in created_microtopics:
                    result_text += f"{mt.number}. {mt.name}\n"
            else:
                skipped = len(lines) - created_count
                result_text = f"✅ Добавлено {created_count} микротем в предмет {subject_name}:\n"
                for mt in created_microtopics:
                    result_text += f"{mt.number}. {mt.name}\n"
                result_text += f"⚠️ Пропущено пустых строк: {skipped}"

        # Получаем обновленный список микротем
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)

        await state.set_state(ManagerTopicStates.topics_list)
        await message.answer(
            text=result_text,
            reply_markup=await get_topics_list_kb(subject_name, microtopics)
        )

    except ValueError as e:
        # Ошибка при создании
        await message.answer(
            text=f"❌ {str(e)}\n\nПопробуйте еще раз:",
            reply_markup=get_home_kb()
        )



def paginate_microtopics(microtopics: list, page: int = 0, per_page: int = 30) -> tuple[list, int, bool, bool]:
    """
    Разбивает список микротем на страницы

    Args:
        microtopics: Список микротем
        page: Номер страницы (начиная с 0)
        per_page: Количество элементов на странице

    Returns:
        tuple: (элементы_страницы, общее_количество_страниц, есть_предыдущая, есть_следующая)
    """
    total_pages = (len(microtopics) + per_page - 1) // per_page if microtopics else 1

    # Проверяем границы
    if page < 0:
        page = 0
    elif page >= total_pages:
        page = total_pages - 1

    start_idx = page * per_page
    end_idx = start_idx + per_page
    page_items = microtopics[start_idx:end_idx]

    has_prev = page > 0
    has_next = page < total_pages - 1

    return page_items, total_pages, has_prev, has_next


@router.callback_query(StateFilter(ManagerTopicStates.topics_list), TopicCallback.filter(F.action == TopicActions.SHOW_LIST))
async def show_microtopics_list(callback: CallbackQuery, callback_data: TopicCallback, state: FSMContext):
    """Показываем список всех микротем предмета в текстовом виде с пагинацией"""
    subject_id = int(callback_data.subject)
    page = int(callback_data.topic) if callback_data.topic else 0

    # Получаем предмет и его микротемы
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_manager_main_menu_kb()
        )
        return

    microtopics = await MicrotopicRepository.get_by_subject(subject_id)

    if not microtopics:
        text = f"📋 Микротемы по предмету {subject.name}:\n\n❌ Микротемы не найдены"
        keyboard = await get_topics_list_kb(subject.name, microtopics)
    else:
        # Используем пагинацию
        page_items, total_pages, has_prev, has_next = paginate_microtopics(microtopics, page)

        text = f"📋 Микротемы по предмету {subject.name}:\n"
        text += f"Страница {page + 1} из {total_pages} (всего: {len(microtopics)})\n\n"

        for microtopic in page_items:
            text += f"{microtopic.number}. {microtopic.name}\n"

        # Создаем клавиатуру с навигацией
        keyboard = await get_topics_list_with_pagination_kb(
            subject.name, microtopics, subject_id, page, has_prev, has_next
        )

    await callback.message.edit_text(
        text=text,
        reply_markup=keyboard
    )

@router.callback_query(StateFilter(ManagerTopicStates.topics_list), TopicCallback.filter(F.action == TopicActions.DELETE_MULTIPLE))
async def start_delete_multiple(callback: CallbackQuery, callback_data: TopicCallback, state: FSMContext):
    """Начинаем процесс удаления микротем"""
    subject_id = int(callback_data.subject)

    # Получаем название предмета для отображения
    subject = await SubjectRepository.get_by_id(subject_id)
    if not subject:
        await callback.message.edit_text(
            text="❌ Предмет не найден!",
            reply_markup=get_manager_main_menu_kb()
        )
        return

    await state.set_state(ManagerTopicStates.delete_multiple)
    await state.update_data(subject_id=subject_id, subject_name=subject.name)

    await callback.message.edit_text(
        text=f"Предмет: {subject.name}\n\n"
             f"🗑 Введите номера микротем для удаления:\n"
             f"• Номера через пробел в одной строке\n"
             f"• Можно ввести до 200 номеров за раз\n"
             f"• Несуществующие номера будут проигнорированы\n\n"
             f"Пример:\n"
             f"1 5 12 25 33",
        reply_markup=get_home_kb()
    )

@router.message(StateFilter(ManagerTopicStates.delete_multiple))
async def process_delete_multiple_numbers(message: Message, state: FSMContext):
    """Обрабатываем ввод номеров микротем для удаления (через пробелы в одной строке)"""
    data = await state.get_data()
    subject_id = data['subject_id']
    subject_name = data['subject_name']

    # Разбиваем текст по пробелам и очищаем от пустых
    parts = [part.strip() for part in message.text.split() if part.strip()]

    if not parts:
        await message.answer(
            text="❌ Введите хотя бы один номер микротемы:",
            reply_markup=get_home_kb()
        )
        return

    # Проверяем лимит
    if len(parts) > 200:
        await message.answer(
            text=f"❌ Слишком много номеров за раз (максимум 200, введено {len(parts)}).\n"
                 f"Разделите на несколько сообщений:",
            reply_markup=get_home_kb()
        )
        return

    # Парсим номера и проверяем их корректность
    numbers = []
    invalid_parts = []

    for part in parts:
        try:
            number = int(part)
            if number < 1:
                invalid_parts.append(f"'{part}' - номер должен быть больше 0")
            else:
                numbers.append(number)
        except ValueError:
            invalid_parts.append(f"'{part}' - не является числом")

    # Если есть ошибки в формате, сообщаем об этом
    if invalid_parts:
        error_text = "❌ Найдены ошибки в формате:\n" + "\n".join(invalid_parts[:10])
        if len(invalid_parts) > 10:
            error_text += f"\n... и еще {len(invalid_parts) - 10} ошибок"
        error_text += "\n\nПопробуйте еще раз:"

        await message.answer(
            text=error_text,
            reply_markup=get_home_kb()
        )
        return

    if not numbers:
        await message.answer(
            text="❌ Не найдено ни одного корректного номера микротемы:",
            reply_markup=get_home_kb()
        )
        return

    try:
        # Удаляем дубликаты номеров, сохраняя порядок
        unique_numbers = list(dict.fromkeys(numbers))

        # Удаляем микротемы по номерам
        deleted_microtopics, not_found_numbers = await MicrotopicRepository.delete_multiple_by_numbers(subject_id, unique_numbers)

        # Формируем результат
        result_text = ""

        if deleted_microtopics:
            result_text += f"✅ Удалено {len(deleted_microtopics)} микротем из предмета {subject_name}:\n"
            for number, name in deleted_microtopics:
                result_text += f"{number}. {name}\n"

        if not_found_numbers:
            result_text += f"\n⚠️ Не найдены микротемы с номерами: {', '.join(map(str, not_found_numbers))}"

        if not deleted_microtopics:
            result_text = f"❌ Ни одна микротема не была удалена из предмета {subject_name}.\n"
            result_text += f"Микротемы с номерами {', '.join(map(str, not_found_numbers))} не найдены."

        # Получаем обновленный список микротем
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)

        await state.set_state(ManagerTopicStates.topics_list)
        await message.answer(
            text=result_text,
            reply_markup=await get_topics_list_kb(subject_name, microtopics)
        )

    except Exception as e:
        # Ошибка при удалении
        await message.answer(
            text=f"❌ Ошибка при удалении микротем: {str(e)}\n\nПопробуйте еще раз:",
            reply_markup=get_home_kb()
        )
