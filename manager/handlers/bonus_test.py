
from aiogram import Router, F
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext
import logging
from typing import Dict

from common.keyboards import get_main_menu_back_button, get_home_kb
from common.manager_tests.register_handlers import register_test_handlers
from .main import show_manager_main_menu

from aiogram.fsm.state import State, StatesGroup

class BonusTestStates(StatesGroup):
    main = State()
    select_course = State()
    select_subject = State()
    select_lesson = State()
    choose_creation_method = State()  # Новое состояние для выбора способа создания
    enter_test_name = State()
    add_question = State()
    select_topic = State()
    enter_question_text = State()
    add_question_photo = State()
    enter_answer_options = State()
    select_correct_answer = State()
    set_time_limit = State()
    confirm_test = State()
    enter_price = State()
    delete_test = State()
    select_test_to_delete = State()
    request_topic = State()
    process_topic = State()
    process_photo = State()
    skip_photo = State()

    # Состояния для создания по шаблону
    bulk_template_input = State()  # Ввод текстового шаблона
    bulk_photo_upload = State()    # Загрузка фото для вопросов с "+"

    # Состояния для создания по шаблону
    bulk_template_input = State()  # Ввод текстового шаблона
    bulk_photo_upload = State()    # Загрузка фото для вопросов с "+"

# Настройка логгера
logger = logging.getLogger(__name__)

router = Router()

def get_bonus_test_management_kb() -> InlineKeyboardMarkup:
    """Клавиатура управления бонусными тестами"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="➕ Добавить бонусный тест", callback_data="add_bonus_test")],
        [InlineKeyboardButton(text="🗑 Удалить бонусный тест", callback_data="delete_bonus_test")],
        *get_main_menu_back_button()
    ])

def get_bonus_creation_method_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора способа создания бонусного теста"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="➕ Добавить вопросы вручную", callback_data="bonus_manual_creation")],
        [InlineKeyboardButton(text="📝 Создать из текстового шаблона", callback_data="bonus_bulk_creation")],
        *get_main_menu_back_button()
    ])

def get_price_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора цены в монетах"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="50 монет", callback_data="price_50")],
        [InlineKeyboardButton(text="100 монет", callback_data="price_100")],
        [InlineKeyboardButton(text="150 монет", callback_data="price_150")],
        [InlineKeyboardButton(text="200 монет", callback_data="price_200")],
        [InlineKeyboardButton(text="Ввести вручную", callback_data="price_custom")],
        *get_main_menu_back_button()
    ])

def get_confirm_bonus_test_kb() -> InlineKeyboardMarkup:
    """Клавиатура подтверждения создания бонусного теста"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Создать тест", callback_data="confirm_bonus_test")],
        [InlineKeyboardButton(text="✏️ Редактировать", callback_data="edit_bonus_test")],
        [InlineKeyboardButton(text="❌ Отмена", callback_data="cancel_bonus_test")]
    ])

def get_confirm_bonus_partial_template_kb() -> InlineKeyboardMarkup:
    """Клавиатура для подтверждения частичного шаблона бонусного теста"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="✅ Подтвердить и создать тест", callback_data="confirm_bonus_partial_template")],
        *get_main_menu_back_button()
    ])

async def get_bonus_tests_list_kb() -> InlineKeyboardMarkup:
    """Клавиатура со списком бонусных тестов для удаления"""
    from database import BonusTestRepository

    try:
        bonus_tests = await BonusTestRepository.get_all()

        if not bonus_tests:
            return InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="📝 Нет бонусных тестов", callback_data="no_tests")],
                *get_main_menu_back_button()
            ])

        keyboard = []
        for test in bonus_tests:
            # Получаем количество вопросов
            question_count = len(test.questions) if test.questions else 0
            button_text = f"🧪 {test.name} - {test.price} монет ({question_count} вопр.)"
            keyboard.append([InlineKeyboardButton(
                text=button_text,
                callback_data=f"delete_bonus_{test.id}"
            )])

        keyboard.extend(get_main_menu_back_button())
        return InlineKeyboardMarkup(inline_keyboard=keyboard)

    except Exception as e:
        logger.error(f"Ошибка при получении списка бонусных тестов: {e}")
        return InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="❌ Ошибка загрузки", callback_data="error_loading")],
            *get_main_menu_back_button()
        ])

@router.callback_query(F.data == "manager_bonus_test")
async def show_bonus_test_management(callback: CallbackQuery, state: FSMContext):
    """Показ меню управления бонусными тестами"""
    logger.info("Вызван обработчик show_bonus_test_management")

    await callback.message.edit_text(
        "🧪 Управление бонусными тестами\n\n"
        "Бонусные тесты появляются в магазине у учеников и покупаются за монеты.",
        reply_markup=get_bonus_test_management_kb()
    )
    await state.set_state(BonusTestStates.main)

@router.callback_query(BonusTestStates.main, F.data == "add_bonus_test")
async def start_add_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Начало добавления бонусного теста - выбор способа создания"""
    logger.info("🚀 СТАРТ: Начинаем создание бонусного теста")

    await callback.message.edit_text(
        "🧪 Создание бонусного теста\n\n"
        "Выберите способ создания:",
        reply_markup=get_bonus_creation_method_kb()
    )
    await state.set_state(BonusTestStates.choose_creation_method)
    logger.info(f"🔄 СОСТОЯНИЕ: Установлено состояние {BonusTestStates.choose_creation_method}")

@router.callback_query(BonusTestStates.choose_creation_method, F.data == "bonus_manual_creation")
async def choose_bonus_manual_creation(callback: CallbackQuery, state: FSMContext):
    """Выбор ручного создания бонусного теста"""
    logger.info("Вызван обработчик choose_bonus_manual_creation")

    await callback.message.edit_text(
        "🧪 Создание бонусного теста\n\n"
        "Введите название бонусного теста:",
        reply_markup=get_home_kb()
    )
    await state.set_state(BonusTestStates.enter_test_name)

@router.callback_query(BonusTestStates.choose_creation_method, F.data == "bonus_bulk_creation")
async def choose_bonus_bulk_creation(callback: CallbackQuery, state: FSMContext):
    """Выбор создания бонусного теста по шаблону"""
    logger.info("Вызван обработчик choose_bonus_bulk_creation")

    # Показываем пример шаблона для бонусного теста
    template_example = """📝 ПРИМЕР ШАБЛОНА БОНУСНОГО ТЕСТА:

Название теста: Супер-тест по химии
Цена: 50
Вопрос: Что такое алканы?
Вариант: Углеводороды
Вариант: Спирты
Вариант: Кислоты
Правильный ответ на вопрос: A
Время ответа на вопрос: 30
Возможность добавить фото к вопросу: +

Вопрос: Формула метана?
Вариант: CH4
Вариант: C2H6
Правильный ответ на вопрос: A
Время ответа на вопрос: 45
Возможность добавить фото к вопросу: -

📋 Вставьте ваш текст по этому шаблону.

💡 ВАЖНО: Если шаблон длинный и разбивается на несколько сообщений, отправьте все части подряд, а затем нажмите кнопку 'Подтвердить'."""

    await callback.message.edit_text(
        f"🧪 Создание бонусного теста по шаблону\n\n"
        f"{template_example}",
        reply_markup=get_home_kb()
    )
    await state.set_state(BonusTestStates.bulk_template_input)

@router.message(BonusTestStates.bulk_template_input)
async def process_bonus_bulk_template(message: Message, state: FSMContext):
    """Обработка введенного шаблона бонусного теста"""
    logger.info("Вызван обработчик process_bonus_bulk_template")

    template_text = message.text.strip()

    if not template_text:
        await message.answer("❌ Шаблон не может быть пустым. Пожалуйста, введите текст по шаблону:")
        return

    # Проверяем, есть ли уже накопленный текст (для многочастных сообщений)
    user_data = await state.get_data()
    accumulated_text = user_data.get("accumulated_template", "")

    # Добавляем новую часть
    if accumulated_text:
        full_template = accumulated_text + "\n" + template_text
    else:
        full_template = template_text

    # ВСЕГДА накапливаем текст, НЕ парсим сразу
    await state.update_data(accumulated_template=full_template)

    # Показываем что получили часть и предлагаем продолжить или подтвердить
    await message.answer(
        f"📝 Получена часть шаблона ({len(template_text)} символов).\n"
        "Отправьте следующую часть или нажмите 'Подтвердить' для обработки всего шаблона.",
        reply_markup=get_confirm_bonus_partial_template_kb()
    )

async def show_bonus_bulk_confirmation(message: Message, state: FSMContext, parsed_data: dict):
    """Показ подтверждения для массового создания бонусного теста"""

    # Формируем информацию о времени для каждого вопроса
    questions_info = ""
    for i, question in enumerate(parsed_data["questions"], 1):
        time_limit = question.get("time_limit", 30)
        time_text = f"{time_limit} сек."
        if time_limit >= 60:
            minutes = time_limit // 60
            seconds = time_limit % 60
            time_text = f"{minutes} мин."
            if seconds > 0:
                time_text += f" {seconds} сек."

        photo_icon = "📸" if question.get("needs_photo", False) else ""
        questions_info += f"Вопрос {i}: {time_text} {photo_icon}\n"

    confirmation_text = (
        f"🧪 ПРЕДВАРИТЕЛЬНЫЙ ПРОСМОТР БОНУСНОГО ТЕСТА\n\n"
        f"📄 Название теста: {parsed_data['test_name']}\n"
        f"💰 Цена: {parsed_data['price']} монет\n"
        f"❓ Количество вопросов: {len(parsed_data['questions'])}\n\n"
        f"⏱ Время на ответ:\n{questions_info}\n"
        "Подтвердите создание бонусного теста:"
    )

    await message.answer(
        confirmation_text,
        reply_markup=get_confirm_bonus_test_kb()
    )
    await state.set_state(BonusTestStates.enter_price)

@router.message(BonusTestStates.bulk_photo_upload)
async def process_bonus_bulk_photo(message: Message, state: FSMContext):
    """Обработка загрузки фото для вопросов бонусного теста"""
    logger.info("Вызван обработчик process_bonus_bulk_photo")

    if not message.photo:
        await message.answer("❌ Пожалуйста, отправьте фото:")
        return

    user_data = await state.get_data()
    photo_questions = user_data.get("photo_questions", [])
    current_index = user_data.get("current_photo_index", 0)
    questions = user_data.get("questions", [])

    if current_index >= len(photo_questions):
        await message.answer("❌ Ошибка: все фото уже загружены.")
        return

    # Сохраняем фото для текущего вопроса
    photo = message.photo[-1]
    file_id = photo.file_id

    current_question = photo_questions[current_index]

    # Находим этот вопрос в общем списке и добавляем фото
    for question in questions:
        if question["text"] == current_question["text"]:
            question["photo_id"] = file_id
            break

    # Переходим к следующему вопросу с фото
    next_index = current_index + 1

    if next_index < len(photo_questions):
        # Есть еще вопросы с фото
        next_question = photo_questions[next_index]
        # ВАЖНО: Обновляем questions в состоянии после каждого фото
        await state.update_data(current_photo_index=next_index, questions=questions)

        await message.answer(
            f"✅ Фото добавлено!\n\n"
            f"📷 Прикрепите фото к следующему вопросу ({next_index + 1}/{len(photo_questions)}):\n\n"
            f"❓ {next_question['text']}",
            reply_markup=get_home_kb()
        )
    else:
        # Все фото загружены, переходим к подтверждению
        await state.update_data(questions=questions)

        parsed_data = {
            "test_name": user_data.get("test_name"),
            "price": user_data.get("price"),
            "questions": questions
        }

        await message.answer("✅ Все фото загружены!")
        await show_bonus_bulk_confirmation(message, state, parsed_data)

@router.callback_query(BonusTestStates.bulk_template_input, F.data == "confirm_bonus_partial_template")
async def confirm_bonus_partial_template(callback: CallbackQuery, state: FSMContext):
    """Подтверждение частичного шаблона бонусного теста"""
    logger.info("Вызван обработчик confirm_bonus_partial_template")

    user_data = await state.get_data()
    accumulated_text = user_data.get("accumulated_template", "")

    if not accumulated_text:
        await callback.message.edit_text("❌ Нет накопленного шаблона для обработки.")
        return

    # Импортируем парсер
    from common.manager_tests.template_parser import parse_bonus_test_template

    # Парсим накопленный шаблон
    success, parsed_data, errors = await parse_bonus_test_template(accumulated_text)

    if not success:
        error_text = "❌ Найдены ошибки в шаблоне:\n\n" + "\n".join(errors)
        error_text += "\n\n📝 Пожалуйста, исправьте ошибки и отправьте шаблон заново:"

        await state.update_data(accumulated_template="")
        await callback.message.edit_text(error_text, reply_markup=get_home_kb())
        return

    # Очищаем накопленный текст
    await state.update_data(accumulated_template="")

    # Сохраняем распарсенные данные
    await state.update_data(
        test_name=parsed_data["test_name"],
        price=parsed_data["price"],
        questions=parsed_data["questions"],
        bulk_creation=True
    )

    # Проверяем, есть ли вопросы с фото
    questions_with_photo = [q for q in parsed_data["questions"] if q.get("needs_photo", False)]

    if questions_with_photo:
        # Переходим к загрузке фото
        await state.update_data(
            photo_questions=questions_with_photo,
            current_photo_index=0
        )

        first_question = questions_with_photo[0]
        await callback.message.edit_text(
            f"✅ Шаблон успешно обработан!\n"
            f"🧪 Название теста: {parsed_data['test_name']}\n"
            f"💰 Цена: {parsed_data['price']} монет\n"
            f"❓ Вопросов: {len(parsed_data['questions'])}\n"
            f"📸 Вопросов с фото: {len(questions_with_photo)}\n\n"
            f"📷 Прикрепите фото к вопросу:\n\n"
            f"❓ {first_question['text']}",
            reply_markup=get_home_kb()
        )
        await state.set_state(BonusTestStates.bulk_photo_upload)
    else:
        # Сразу переходим к подтверждению
        await callback.message.edit_text("✅ Обрабатываем шаблон...")
        await show_bonus_bulk_confirmation(callback.message, state, parsed_data)

# Регистрируем общие обработчики тестов
logger.info("🔧 РЕГИСТРАЦИЯ: Регистрируем обработчики для бонусных тестов с role='bonus_test'")
register_test_handlers(router, BonusTestStates, "bonus_test")

@router.callback_query(BonusTestStates.confirm_test, F.data == "confirm_test")
async def set_bonus_test_price(callback: CallbackQuery, state: FSMContext):
    """Переход к установке цены в монетах после сохранения в общем модуле"""
    logger.info("Вызван обработчик set_bonus_test_price")
    
    user_data = await state.get_data()
    test_name = user_data.get("test_name", "")
    questions = user_data.get("questions", [])
    time_limit = user_data.get("time_limit", 0)
    
    # Форматируем время
    time_text = f"{time_limit} сек."
    if time_limit >= 60:
        minutes = time_limit // 60
        seconds = time_limit % 60
        time_text = f"{minutes} мин."
        if seconds > 0:
            time_text += f" {seconds} сек."
    
    await callback.message.edit_text(
        f"🧪 Название теста: {test_name}\n"
        f"📋 Количество вопросов: {len(questions)}\n"
        "Выберите цену в монетах для этого бонусного теста:",
        reply_markup=get_price_kb()
    )
    await state.set_state(BonusTestStates.enter_price)

@router.callback_query(BonusTestStates.enter_price, F.data.startswith("price_"))
async def process_price_selection(callback: CallbackQuery, state: FSMContext):
    """Обработка выбора цены"""
    logger.info("Вызван обработчик process_price_selection")
    
    price_data = callback.data.replace("price_", "")
    
    if price_data == "custom":
        await callback.message.edit_text(
            "Введите цену в монетах (число):",
            reply_markup=get_home_kb()
        )
        return
    
    price = int(price_data)
    await state.update_data(price=price)
    await show_bonus_test_confirmation(callback, state)

@router.message(BonusTestStates.enter_price)
async def process_custom_price(message: Message, state: FSMContext):
    """Обработка ввода пользовательской цены"""
    logger.info("Вызван обработчик process_custom_price")
    
    try:
        price = int(message.text.strip())
        if price <= 0:
            await message.answer("Цена должна быть положительным числом. Попробуйте еще раз:")
            return
            
        await state.update_data(price=price)
        await show_bonus_test_confirmation(message, state)
    except ValueError:
        await message.answer("Пожалуйста, введите корректное число:")

async def show_bonus_test_confirmation(message_or_callback, state: FSMContext):
    """Показ подтверждения создания бонусного теста"""
    user_data = await state.get_data()
    test_name = user_data.get("test_name", "")
    questions = user_data.get("questions", [])
    time_limit = user_data.get("time_limit", 0)
    price = user_data.get("price", 0)
    
    # Форматируем время
    time_text = f"{time_limit} сек."
    if time_limit >= 60:
        minutes = time_limit // 60
        seconds = time_limit % 60
        time_text = f"{minutes} мин."
        if seconds > 0:
            time_text += f" {seconds} сек."
    
    confirmation_text = (
        f"🧪 Бонусный тест готов к созданию:\n\n"
        f"📝 Название: {test_name}\n"
        f"📋 Количество вопросов: {len(questions)}\n"
        f"💰 Цена: {price} монет\n\n"
        "Подтвердите создание бонусного теста:"
    )
    
    if hasattr(message_or_callback, 'message'):
        await message_or_callback.message.edit_text(
            confirmation_text,
            reply_markup=get_confirm_bonus_test_kb()
        )
    else:
        await message_or_callback.answer(
            confirmation_text,
            reply_markup=get_confirm_bonus_test_kb()
        )

@router.callback_query(BonusTestStates.enter_price, F.data == "confirm_bonus_test")
async def save_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Сохранение бонусного теста"""
    logger.info("Вызван обработчик save_bonus_test")

    try:
        user_data = await state.get_data()
        test_name = user_data.get("test_name", "")
        price = user_data.get("price", 0)
        questions = user_data.get("questions", [])

        # Импортируем репозитории
        from database import BonusTestRepository, BonusQuestionRepository, BonusAnswerOptionRepository

        # Создаем бонусный тест
        bonus_test = await BonusTestRepository.create(name=test_name, price=price)
        logger.info(f"✅ Создан бонусный тест: {bonus_test.id} - {bonus_test.name}")

        # Создаем вопросы и варианты ответов
        for question_data in questions:
            # Получаем photo_path из photo_id (file_id от Telegram)
            photo_path = question_data.get("photo_id")

            # Создаем вопрос
            bonus_question_repo = BonusQuestionRepository()
            question = await bonus_question_repo.create(
                bonus_test_id=bonus_test.id,
                text=question_data.get("text", ""),
                photo_path=photo_path,
                time_limit=question_data.get("time_limit", 30)
            )

            # Создаем варианты ответов
            answer_options = []
            for letter, text in question_data.get("options", {}).items():
                is_correct = (letter == question_data.get("correct_answer"))
                answer_options.append({
                    'text': text,
                    'is_correct': is_correct
                })

            if answer_options:
                await BonusAnswerOptionRepository.create_multiple(question.id, answer_options)

        await callback.message.edit_text(
            f"✅ Бонусный тест '{test_name}' успешно создан!\n"
            f"💰 Цена: {price} монет\n"
            f"📋 Вопросов: {len(questions)}\n\n"
            "Тест появится в каталоге бонусов у учеников.",
            reply_markup=get_bonus_test_management_kb()
        )
        await state.set_state(BonusTestStates.main)

    except Exception as e:
        logger.error(f"❌ Ошибка при сохранении бонусного теста: {e}")
        await callback.message.edit_text(
            f"❌ Ошибка при создании бонусного теста: {str(e)}\n\n"
            "Попробуйте еще раз.",
            reply_markup=get_bonus_test_management_kb()
        )
        await state.set_state(BonusTestStates.main)

@router.callback_query(BonusTestStates.enter_price, F.data == "edit_bonus_test")
async def edit_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Редактирование бонусного теста"""
    logger.info("Вызван обработчик edit_bonus_test")
    
    await callback.message.edit_text(
        "Выберите цену в монетах для этого бонусного теста:",
        reply_markup=get_price_kb()
    )

@router.callback_query(BonusTestStates.enter_price, F.data == "cancel_bonus_test")
async def cancel_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Отмена создания бонусного теста"""
    logger.info("Вызван обработчик cancel_bonus_test")
    
    await callback.message.edit_text(
        "❌ Создание бонусного теста отменено.",
        reply_markup=get_bonus_test_management_kb()
    )
    await state.set_state(BonusTestStates.main)

# Обработчики для удаления бонусных тестов
@router.callback_query(BonusTestStates.main, F.data == "delete_bonus_test")
async def show_bonus_tests_to_delete(callback: CallbackQuery, state: FSMContext):
    """Показ списка бонусных тестов для удаления"""
    logger.info("Вызван обработчик show_bonus_tests_to_delete")

    try:
        tests_kb = await get_bonus_tests_list_kb()

        await callback.message.edit_text(
            "🗑 Выберите бонусный тест для удаления:\n\n"
            "⚠️ Внимание: удаление нельзя отменить!",
            reply_markup=tests_kb
        )
        await state.set_state(BonusTestStates.select_test_to_delete)

    except Exception as e:
        logger.error(f"Ошибка при показе списка тестов для удаления: {e}")
        await callback.message.edit_text(
            "❌ Произошла ошибка при загрузке списка тестов.",
            reply_markup=get_bonus_test_management_kb()
        )

@router.callback_query(BonusTestStates.select_test_to_delete, F.data.startswith("delete_bonus_"))
async def confirm_delete_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Подтверждение удаления бонусного теста"""
    logger.info("Вызван обработчик confirm_delete_bonus_test")

    try:
        test_id = int(callback.data.replace("delete_bonus_", ""))
        await state.update_data(bonus_test_id=test_id)

        # Получаем информацию о тесте из базы данных
        from database import BonusTestRepository
        bonus_test = await BonusTestRepository.get_by_id(test_id)

        if not bonus_test:
            await callback.message.edit_text(
                "❌ Бонусный тест не найден.",
                reply_markup=await get_bonus_tests_list_kb()
            )
            return

        # Получаем количество вопросов
        question_count = len(bonus_test.questions) if bonus_test.questions else 0

        await callback.message.edit_text(
            f"⚠️ Подтверждение удаления\n\n"
            f"🧪 Название: {bonus_test.name}\n"
            f"💰 Цена: {bonus_test.price} монет\n"
            f"📋 Вопросов: {question_count}\n\n"
            f"Вы действительно хотите удалить этот бонусный тест?\n"
            f"❗ Это действие нельзя отменить!",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="✅ Да, удалить", callback_data="confirm_delete_bonus")],
                [InlineKeyboardButton(text="❌ Нет, отмена", callback_data="cancel_delete_bonus")]
            ])
        )

    except ValueError:
        await callback.message.edit_text(
            "❌ Некорректный ID теста.",
            reply_markup=await get_bonus_tests_list_kb()
        )
    except Exception as e:
        logger.error(f"Ошибка при подтверждении удаления: {e}")
        await callback.message.edit_text(
            "❌ Произошла ошибка при загрузке информации о тесте.",
            reply_markup=await get_bonus_tests_list_kb()
        )

@router.callback_query(F.data == "confirm_delete_bonus")
async def delete_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Удаление бонусного теста"""
    logger.info("Вызван обработчик delete_bonus_test")

    try:
        user_data = await state.get_data()
        bonus_test_id = user_data.get("bonus_test_id")

        if not bonus_test_id:
            await callback.message.edit_text(
                "❌ Ошибка: ID теста не найден.",
                reply_markup=get_bonus_test_management_kb()
            )
            return

        # Получаем информацию о тесте перед удалением
        from database import BonusTestRepository
        bonus_test = await BonusTestRepository.get_by_id(bonus_test_id)
        test_name = bonus_test.name if bonus_test else "Неизвестный тест"

        # Удаляем бонусный тест (каскадно удалятся вопросы и варианты ответов)
        success = await BonusTestRepository.delete(bonus_test_id)

        if success:
            await callback.message.edit_text(
                f"✅ Бонусный тест '{test_name}' успешно удален!\n\n"
                f"Тест больше не будет доступен ученикам в каталоге бонусов.",
                reply_markup=get_bonus_test_management_kb()
            )
            logger.info(f"✅ Удален бонусный тест: {test_name} (ID: {bonus_test_id})")
        else:
            await callback.message.edit_text(
                "❌ Не удалось удалить бонусный тест. Попробуйте еще раз.",
                reply_markup=get_bonus_test_management_kb()
            )

        await state.set_state(BonusTestStates.main)

    except Exception as e:
        logger.error(f"Ошибка при удалении бонусного теста: {e}")
        await callback.message.edit_text(
            "❌ Произошла ошибка при удалении бонусного теста.",
            reply_markup=get_bonus_test_management_kb()
        )
        await state.set_state(BonusTestStates.main)

@router.callback_query(F.data == "cancel_delete_bonus")
async def cancel_delete_bonus_test(callback: CallbackQuery, state: FSMContext):
    """Отмена удаления бонусного теста"""
    logger.info("Вызван обработчик cancel_delete_bonus_test")

    await show_bonus_tests_to_delete(callback, state)

# Обработчик для случая, когда нет тестов для удаления
@router.callback_query(F.data == "no_tests")
async def no_tests_handler(callback: CallbackQuery, state: FSMContext):
    """Обработчик для случая отсутствия тестов"""
    await callback.answer("Нет бонусных тестов для удаления")

@router.callback_query(F.data == "error_loading")
async def error_loading_handler(callback: CallbackQuery, state: FSMContext):
    """Обработчик ошибки загрузки"""
    await callback.answer("Ошибка при загрузке списка тестов")
