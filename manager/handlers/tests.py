import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext
from manager.states.states_tests import ManagerTestsStatisticsStates, STATE_TRANSITIONS, STATE_HANDLERS
from common.tests_statistics.register_handlers import register_test_statistics_handlers
from common.tests_statistics.menu import show_tests_statistics_menu
from manager.keyboards.analytics import get_staff_type_selection_kb, get_staff_kb
from common.utils import check_if_id_in_callback_data

# Настраиваем логгер
logger = logging.getLogger(__name__)

router = Router()

# Регистрируем базовые обработчики для менеджера
register_test_statistics_handlers(router, ManagerTestsStatisticsStates, "manager")

# Дополнительные обработчики для выбора персонала

# Обработчики для входного теста курса
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_course_entry_test")
async def manager_course_entry_show_subjects_directly_handler(callback: CallbackQuery, state: FSMContext):
    """Сразу показать предметы для входного теста курса (без выбора персонала)"""
    logger.info("Вызван обработчик manager_course_entry_show_subjects_directly")

    # Сразу переходим к выбору предметов (используем существующий обработчик)
    from common.tests_statistics.handlers import show_course_entry_groups
    await show_course_entry_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_select_subject)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_course_entry, F.data.startswith("staff_type_"))
async def manager_select_staff_for_course_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для входного теста курса"""
    logger.info("Вызван обработчик manager_select_staff_for_course_entry")
    await manager_select_staff_for_course_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_course_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_course_entry, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_course_entry_show_subjects_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору предметов после выбора сотрудника"""
    logger.info("Вызван обработчик manager_course_entry_show_subjects")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору предметов (используем существующий обработчик)
    from common.tests_statistics.handlers import show_course_entry_groups
    await show_course_entry_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_select_subject)

# Обработчики для входного теста месяца
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_month_entry_test")
async def manager_select_staff_type_for_month_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для входного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_type_for_month_entry")
    await manager_select_staff_type_for_month_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_month_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_month_entry, F.data.startswith("staff_type_"))
async def manager_select_staff_for_month_entry_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для входного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_for_month_entry")
    await manager_select_staff_for_month_entry(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_month_entry)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_month_entry, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_month_entry_show_groups_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору групп после выбора сотрудника"""
    logger.info("Вызван обработчик manager_month_entry_show_groups")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору групп (используем существующий обработчик)
    from common.tests_statistics.handlers import show_month_entry_groups
    await show_month_entry_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_select_group)

# Обработчики для контрольного теста месяца
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_month_control_test")
async def manager_select_staff_type_for_month_control_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для контрольного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_type_for_month_control")
    await manager_select_staff_type_for_month_control(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_month_control)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_month_control, F.data.startswith("staff_type_"))
async def manager_select_staff_for_month_control_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для контрольного теста месяца"""
    logger.info("Вызван обработчик manager_select_staff_for_month_control")
    await manager_select_staff_for_month_control(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_month_control)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_month_control, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_month_control_show_groups_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору групп после выбора сотрудника"""
    logger.info("Вызван обработчик manager_month_control_show_groups")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору групп (используем существующий обработчик)
    from common.tests_statistics.handlers import show_month_control_groups
    await show_month_control_groups(callback, state)
    new_state = ManagerTestsStatisticsStates.month_control_select_group
    await state.set_state(new_state)
    logger.info(f"🎯 МЕНЕДЖЕР: Установлено состояние {new_state} для выбора групп контрольного теста месяца")

# Обработчики для пробного ЕНТ
@router.callback_query(ManagerTestsStatisticsStates.main, F.data == "stats_ent_test")
async def manager_select_staff_type_for_ent_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор типа персонала для пробного ЕНТ"""
    logger.info("Вызван обработчик manager_select_staff_type_for_ent")
    await manager_select_staff_type_for_ent(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_type_for_ent)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_type_for_ent, F.data.startswith("staff_type_"))
async def manager_select_staff_for_ent_handler(callback: CallbackQuery, state: FSMContext):
    """Выбор конкретного сотрудника для пробного ЕНТ"""
    logger.info("Вызван обработчик manager_select_staff_for_ent")
    await manager_select_staff_for_ent(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.select_staff_for_ent)

@router.callback_query(ManagerTestsStatisticsStates.select_staff_for_ent, F.data.startswith(("manager_curator_", "manager_teacher_")))
async def manager_ent_show_groups_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору групп после выбора сотрудника"""
    logger.info("Вызван обработчик manager_ent_show_groups")
    
    # Сохраняем выбранного сотрудника
    if callback.data.startswith("manager_curator_"):
        staff_id = await check_if_id_in_callback_data("manager_curator_", callback, state, "curator")
        await state.update_data(selected_curator=staff_id)
    elif callback.data.startswith("manager_teacher_"):
        staff_id = await check_if_id_in_callback_data("manager_teacher_", callback, state, "teacher")
        await state.update_data(selected_teacher=staff_id)
    
    # Переходим к выбору групп (используем существующий обработчик)
    from common.tests_statistics.handlers import show_ent_groups
    await show_ent_groups(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.ent_select_group)

# Дополнительные обработчики для выбора групп и дальнейшей навигации

# Обработчики для входного теста курса - выбор предметов
@router.callback_query(ManagerTestsStatisticsStates.course_entry_select_subject, F.data.startswith("course_entry_subject_"))
async def manager_course_entry_show_users_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору пользователей после выбора предмета"""
    logger.info("Вызван обработчик manager_course_entry_show_users")
    from common.tests_statistics.handlers import show_course_entry_statistics
    await show_course_entry_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_select_user)

# Обработчики для входного теста месяца - выбор групп
@router.callback_query(ManagerTestsStatisticsStates.month_entry_select_group, F.data.startswith("month_entry_group_"))
async def manager_month_entry_show_months_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору месяцев после выбора группы"""
    logger.info("Вызван обработчик manager_month_entry_show_months")
    from common.tests_statistics.handlers import show_month_entry_months
    await show_month_entry_months(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_select_month)

# Обработчики для контрольного теста месяца - выбор групп
@router.callback_query(ManagerTestsStatisticsStates.month_control_select_group, F.data.startswith("month_control_group_"))
async def manager_month_control_show_months_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору месяцев после выбора группы"""
    current_state = await state.get_state()
    logger.info(f"🎯 МЕНЕДЖЕР: Вызван обработчик manager_month_control_show_months, состояние: {current_state}, callback_data: {callback.data}")
    from common.tests_statistics.handlers import show_month_control_months
    await show_month_control_months(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_control_select_month)

# Обработчики для пробного ЕНТ - выбор групп
@router.callback_query(ManagerTestsStatisticsStates.ent_select_group, F.data.startswith("ent_group_"))
async def manager_ent_show_students_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору студентов после выбора группы"""
    logger.info("Вызван обработчик manager_ent_show_students")
    from common.tests_statistics.handlers import show_ent_students
    await show_ent_students(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.ent_select_student)

# Обработчики для выбора месяцев и студентов

# Входной тест месяца - выбор месяцев
@router.callback_query(ManagerTestsStatisticsStates.month_entry_select_month, F.data.startswith("month_entry_month_"))
async def manager_month_entry_show_students_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору студентов после выбора месяца"""
    logger.info("Вызван обработчик manager_month_entry_show_students")
    from common.tests_statistics.handlers import show_month_entry_statistics
    await show_month_entry_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_select_student)

# Контрольный тест месяца - выбор месяцев
@router.callback_query(ManagerTestsStatisticsStates.month_control_select_month, F.data.startswith("month_control_month_"))
async def manager_month_control_show_students_handler(callback: CallbackQuery, state: FSMContext):
    """Переход к выбору студентов после выбора месяца"""
    logger.info("Вызван обработчик manager_month_control_show_students")
    from common.tests_statistics.handlers import show_month_control_statistics
    await show_month_control_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_control_select_student)

# Обработчики для выбора конкретных пользователей/студентов

# Входной тест курса - выбор незарегистрированного пользователя
@router.callback_query(ManagerTestsStatisticsStates.course_entry_select_user, F.data.startswith("course_entry_user_"))
async def manager_course_entry_show_result_handler(callback: CallbackQuery, state: FSMContext):
    """Показать результат входного теста курса для пользователя"""
    logger.info("Вызван обработчик manager_course_entry_show_result")
    from common.tests_statistics.handlers import show_course_entry_student_statistics
    await show_course_entry_student_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_result)

# Входной тест месяца - выбор студента
@router.callback_query(ManagerTestsStatisticsStates.month_entry_select_student, F.data.startswith("month_entry_student_"))
async def manager_month_entry_show_result_handler(callback: CallbackQuery, state: FSMContext):
    """Показать результат входного теста месяца для студента"""
    logger.info("Вызван обработчик manager_month_entry_show_result")
    from common.tests_statistics.handlers import show_month_entry_student_statistics
    await show_month_entry_student_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_result)

# Контрольный тест месяца - выбор студента
@router.callback_query(ManagerTestsStatisticsStates.month_control_select_student, F.data.startswith("month_control_student_"))
async def manager_month_control_show_result_handler(callback: CallbackQuery, state: FSMContext):
    """Показать результат контрольного теста месяца для студента"""
    logger.info("Вызван обработчик manager_month_control_show_result")
    from common.tests_statistics.handlers import show_month_control_student_statistics
    await show_month_control_student_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_control_result)

# Пробный ЕНТ - выбор студента
@router.callback_query(ManagerTestsStatisticsStates.ent_select_student, F.data.startswith("ent_student_"))
async def manager_ent_show_result_handler(callback: CallbackQuery, state: FSMContext):
    """Показать результат пробного ЕНТ для студента"""
    logger.info("Вызван обработчик manager_ent_show_result")
    from common.tests_statistics.handlers import show_ent_statistics
    await show_ent_statistics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.ent_result)

# Обработчики для детальной аналитики

# Входной тест курса - детальная аналитика
@router.callback_query(ManagerTestsStatisticsStates.course_entry_result, F.data.startswith("course_entry_detailed_"))
async def manager_course_entry_detailed_handler(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам входного теста курса"""
    logger.info("Вызван обработчик manager_course_entry_detailed")
    from common.tests_statistics.handlers import show_course_entry_detailed_microtopics
    await show_course_entry_detailed_microtopics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_result_display)

@router.callback_query(ManagerTestsStatisticsStates.course_entry_result, F.data.startswith("course_entry_summary_"))
async def manager_course_entry_summary_handler(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным/слабым темам входного теста курса"""
    logger.info("Вызван обработчик manager_course_entry_summary")
    from common.tests_statistics.handlers import show_course_entry_summary_microtopics
    await show_course_entry_summary_microtopics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.course_entry_result_display)

# Входной тест месяца - детальная аналитика
@router.callback_query(ManagerTestsStatisticsStates.month_entry_result, F.data.startswith("month_entry_detailed_"))
async def manager_month_entry_detailed_handler(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам входного теста месяца"""
    logger.info("Вызван обработчик manager_month_entry_detailed")
    from common.tests_statistics.handlers import show_month_entry_detailed_microtopics
    await show_month_entry_detailed_microtopics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_result_display)

@router.callback_query(ManagerTestsStatisticsStates.month_entry_result, F.data.startswith("month_entry_summary_"))
async def manager_month_entry_summary_handler(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным/слабым темам входного теста месяца"""
    logger.info("Вызван обработчик manager_month_entry_summary")
    from common.tests_statistics.handlers import show_month_entry_summary_microtopics
    await show_month_entry_summary_microtopics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_entry_result_display)

# Контрольный тест месяца - детальная аналитика
@router.callback_query(ManagerTestsStatisticsStates.month_control_result, F.data.startswith("month_control_detailed_"))
async def manager_month_control_detailed_handler(callback: CallbackQuery, state: FSMContext):
    """Показать детальную статистику по микротемам контрольного теста месяца"""
    logger.info("Вызван обработчик manager_month_control_detailed")
    from common.tests_statistics.handlers import show_month_control_detailed_microtopics
    await show_month_control_detailed_microtopics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_control_result_display)

@router.callback_query(ManagerTestsStatisticsStates.month_control_result, F.data.startswith("month_control_summary_"))
async def manager_month_control_summary_handler(callback: CallbackQuery, state: FSMContext):
    """Показать сводку по сильным/слабым темам контрольного теста месяца"""
    logger.info("Вызван обработчик manager_month_control_summary")
    from common.tests_statistics.handlers import show_month_control_summary_microtopics
    await show_month_control_summary_microtopics(callback, state)
    await state.set_state(ManagerTestsStatisticsStates.month_control_result_display)

# Функции-обработчики для состояний (используются в STATE_HANDLERS)
async def manager_select_staff_type_for_course_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для входного теста курса"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики входного теста курса:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_type_for_month_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для входного теста месяца"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики входного теста месяца:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_type_for_month_control(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для контрольного теста месяца"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики контрольного теста месяца:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_type_for_ent(callback: CallbackQuery, state: FSMContext):
    """Показать выбор типа персонала для пробного ЕНТ"""
    await callback.message.edit_text(
        "Выберите тип сотрудников для просмотра статистики пробного ЕНТ:",
        reply_markup=get_staff_type_selection_kb()
    )

async def manager_select_staff_for_course_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для входного теста курса"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики входного теста курса:",
        reply_markup=staff_kb
    )

async def manager_select_staff_for_month_entry(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для входного теста месяца"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики входного теста месяца:",
        reply_markup=staff_kb
    )

async def manager_select_staff_for_month_control(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для контрольного теста месяца"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики контрольного теста месяца:",
        reply_markup=staff_kb
    )

async def manager_select_staff_for_ent(callback: CallbackQuery, state: FSMContext):
    """Показать выбор конкретного сотрудника для пробного ЕНТ"""
    staff_type = callback.data.replace("staff_type_", "")
    await state.update_data(staff_type=staff_type)
    
    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"
    
    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики пробного ЕНТ:",
        reply_markup=staff_kb
    )
