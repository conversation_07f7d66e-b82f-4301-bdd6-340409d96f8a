#!/usr/bin/env python3
"""
Проверка зарегистрированных обработчиков
"""
import asyncio

async def check_handlers():
    """Проверяем зарегистрированные обработчики"""
    print("🔍 Проверяем зарегистрированные обработчики...")
    
    try:
        # Импортируем роутер куратора
        from curator.handlers import router as curator_router
        
        print(f"📋 Роутер куратора: {curator_router}")
        print(f"📋 Количество callback обработчиков: {len(curator_router.callback_query.handlers)}")
        
        # Ищем обработчики с нужными callback_data
        found_handlers = []
        
        for i, handler in enumerate(curator_router.callback_query.handlers):
            print(f"\n🔍 Обработчик #{i}:")
            print(f"   Функция: {handler.callback.__name__ if hasattr(handler.callback, '__name__') else 'unknown'}")
            
            # Проверяем фильтры
            if hasattr(handler, 'filters'):
                for filter_obj in handler.filters:
                    print(f"   Фильтр: {filter_obj}")
                    
                    # Ищем фильтры callback_data
                    if hasattr(filter_obj, 'callback_data'):
                        print(f"   Callback data: {filter_obj.callback_data}")
                        if 'curator_back_to_month_entry_result' in str(filter_obj.callback_data):
                            found_handlers.append(handler)
                    
                    # Ищем фильтры состояний
                    if hasattr(filter_obj, 'state'):
                        print(f"   State: {filter_obj.state}")
        
        print(f"\n✅ Найдено обработчиков с 'curator_back_to_month_entry_result': {len(found_handlers)}")
        
        if found_handlers:
            for handler in found_handlers:
                print(f"   - {handler.callback.__name__}")
        else:
            print("❌ НЕТ ОБРАБОТЧИКОВ с callback 'curator_back_to_month_entry_result'!")
        
        # Проверяем состояния куратора
        from curator.states.states_tests import CuratorTestsStatisticsStates
        
        print(f"\n📊 Состояния куратора:")
        print(f"   - detailed: {CuratorTestsStatisticsStates.month_entry_detailed_microtopics}")
        print(f"   - summary: {CuratorTestsStatisticsStates.month_entry_summary_microtopics}")
        
        return len(found_handlers) > 0
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Главная функция"""
    print("🚀 Проверка обработчиков куратора\n")
    
    success = await check_handlers()
    
    if success:
        print("\n✅ Обработчики найдены!")
    else:
        print("\n❌ Обработчики НЕ НАЙДЕНЫ!")
        print("\n🔧 Возможные причины:")
        print("1. Функция register_curator_month_entry_test_microtopics_handlers не вызывается")
        print("2. Ошибка при регистрации обработчиков")
        print("3. Неправильные параметры при регистрации")

if __name__ == "__main__":
    asyncio.run(main())
