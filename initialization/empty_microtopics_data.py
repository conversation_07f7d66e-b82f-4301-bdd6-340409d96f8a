"""
Создание данных для тестирования пагинации микротем (много сильных и слабых тем)
"""
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    StudentRepository, HomeworkResultRepository, QuestionResultRepository,
    SubjectRepository, get_db_session
)
from database.models import QuestionResult, HomeworkResult, Question
from sqlalchemy import select, and_


async def create_empty_microtopics_data():
    """Создание данных с большим количеством сильных и слабых тем для тестирования пагинации"""
    try:
        print("🧪 Создание данных для тестирования пагинации микротем...")
        
        # Находим студента с telegram_id 955518340 (Андрей Климов)
        student = await StudentRepository.get_by_telegram_id(955518340)
        if not student:
            print("❌ Студент с telegram_id 955518340 не найден")
            return
        
        print(f"✅ Найден студент: {student.user.name} (ID: {student.id})")
        
        # Получаем предмет математика
        subjects = await SubjectRepository.get_all()
        math_subject = next((s for s in subjects if s.name == "Математика"), None)
        
        if not math_subject:
            print("❌ Предмет 'Математика' не найден")
            return
        
        print(f"✅ Найден предмет: {math_subject.name} (ID: {math_subject.id})")
        
        # Получаем текущую статистику
        microtopic_stats = await HomeworkResultRepository.get_microtopic_understanding(
            student.id, math_subject.id
        )
        
        if not microtopic_stats:
            print("❌ Нет данных по микротемам для изменения")
            return
        
        strong_topics = {k: v for k, v in microtopic_stats.items() if v['percentage'] >= 80}
        weak_topics = {k: v for k, v in microtopic_stats.items() if v['percentage'] <= 40}
        
        print(f"📊 Текущая статистика:")
        print(f"   🟢 Сильные темы (≥80%): {len(strong_topics)}")
        print(f"   🔴 Слабые темы (≤40%): {len(weak_topics)}")

        # Получаем все результаты ДЗ студента по математике
        homework_results = await HomeworkResultRepository.get_by_student(student.id)
        math_homework_results = []

        from database import HomeworkRepository
        for hr in homework_results:
            homework = await HomeworkRepository.get_by_id(hr.homework_id)
            if homework.subject_id == math_subject.id:
                math_homework_results.append(hr)

        print(f"📝 Найдено результатов ДЗ по математике: {len(math_homework_results)}")

        # Создаем ДОПОЛНИТЕЛЬНЫЕ результаты для микротем 1-60
        print("🔧 Создаем дополнительные результаты для микротем 1-60...")

        # Получаем первое ДЗ по математике для создания дополнительных результатов
        if math_homework_results:
            base_homework_result = math_homework_results[0]

            # Создаем по 5 дополнительных результатов для каждой микротемы 1-60
            created_count = 0
            for microtopic_num in range(1, 61):  # Микротемы 1-60
                for i in range(5):  # По 5 результатов на микротему
                    # Определяем правильность ответа
                    if microtopic_num <= 30:
                        # Сильные темы - 90% правильных
                        is_correct = i < 4  # 4 из 5 правильных
                    else:
                        # Слабые темы - 20% правильных
                        is_correct = i < 1  # 1 из 5 правильных

                    try:
                        await QuestionResultRepository.create(
                            homework_result_id=base_homework_result.id,
                            question_id=1,  # Используем любой существующий вопрос
                            is_correct=is_correct,
                            microtopic_number=microtopic_num
                        )
                        created_count += 1
                    except Exception as e:
                        print(f"      ❌ Ошибка при создании результата для микротемы {microtopic_num}: {e}")

            print(f"✅ Создано дополнительных результатов: {created_count}")

        # Также изменяем существующие результаты
        updated_count = 0
        for homework_result in math_homework_results:
            question_results = await QuestionResultRepository.get_by_homework_result(homework_result.id)

            for qr in question_results:
                if qr.microtopic_number:
                    if qr.microtopic_number <= 30:
                        new_is_correct = True  # Сильные темы
                    elif 31 <= qr.microtopic_number <= 60:
                        new_is_correct = False  # Слабые темы
                    else:
                        import random
                        new_is_correct = random.choice([True, False])  # Средние темы

                    await QuestionResultRepository.update(
                        qr.id,
                        is_correct=new_is_correct
                    )
                    updated_count += 1

        print(f"✅ Обновлено существующих результатов: {updated_count}")
        
        # Проверяем новую статистику
        new_microtopic_stats = await HomeworkResultRepository.get_microtopic_understanding(
            student.id, math_subject.id
        )
        
        new_strong_topics = {k: v for k, v in new_microtopic_stats.items() if v['percentage'] >= 80}
        new_weak_topics = {k: v for k, v in new_microtopic_stats.items() if v['percentage'] <= 40}
        new_medium_topics = {k: v for k, v in new_microtopic_stats.items() if 40 < v['percentage'] < 80}
        
        print(f"\n📊 Новая статистика:")
        print(f"   🟢 Сильные темы (≥80%): {len(new_strong_topics)}")
        print(f"   🟡 Средние темы (41-79%): {len(new_medium_topics)}")
        print(f"   🔴 Слабые темы (≤40%): {len(new_weak_topics)}")

        # Проверяем, достаточно ли данных для пагинации
        total_summary_items = len(new_strong_topics) + len(new_weak_topics)
        if total_summary_items > 15:
            print(f"🎉 Отлично! Пагинация сводки будет работать!")
            print(f"📄 Всего элементов в сводке: {total_summary_items} (больше 15)")
            print("📄 Теперь можно тестировать:")
            print("   1. Сводку сильных и слабых тем с пагинацией")
            print("   2. Переключение страниц в сводке")
            print("   3. Детальную статистику с пагинацией")
        else:
            print(f"📄 Элементов в сводке: {total_summary_items}. Для пагинации нужно >15")

        print("✅ Создание данных для тестирования пагинации завершено!")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import asyncio
    asyncio.run(create_empty_microtopics_data())
