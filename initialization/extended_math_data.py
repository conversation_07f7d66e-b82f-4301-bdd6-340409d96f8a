"""
Расширенные данные по математике для тестирования пагинации микротем
"""
from database import (
    MicrotopicRepository, LessonRepository, HomeworkRepository, 
    QuestionRepository, AnswerOptionRepository, StudentRepository,
    HomeworkResultRepository, QuestionResultRepository
)
import random


async def create_extended_math_data():
    """Создание расширенных данных по математике"""
    try:
        print("🔢 Создание расширенных данных по математике...")
        
        # Получаем предмет математика
        from database import SubjectRepository
        subjects = await SubjectRepository.get_all()
        math_subject = next((s for s in subjects if s.name == "Математика"), None)
        
        if not math_subject:
            print("❌ Предмет 'Математика' не найден")
            return
            
        print(f"📚 Найден предмет: {math_subject.name} (ID: {math_subject.id})")
        
        # Расширенный список микротем для математики (50 микротем)
        extended_microtopics = [
            "Натуральные числа", "Целые числа", "Рациональные числа", "Иррациональные числа",
            "Действительные числа", "Комплексные числа", "Степени и корни", "Логарифмы и экспоненты",
            "Линейные уравнения", "Квадратные уравнения", "Кубические уравнения", "Системы уравнений",
            "Неравенства", "Модуль числа", "Прогрессии", "Многочлены",
            "Рациональные функции", "Тригонометрические функции", "Обратные тригонометрические функции",
            "Показательные функции", "Логарифмические функции", "Предел функции",
            "Непрерывность функции", "Производная", "Правила дифференцирования", "Применение производной",
            "Первообразная", "Неопределенный интеграл", "Определенный интеграл", "Применение интеграла",
            "Точка на плоскости", "Прямая на плоскости", "Окружность", "Эллипс",
            "Парабола", "Гипербола", "Треугольники", "Четырехугольники",
            "Многоугольники", "Площади фигур", "Объемы тел", "Векторы на плоскости",
            "Векторы в пространстве", "Скалярное произведение", "Векторное произведение", "Матрицы",
            "Определители", "Системы линейных уравнений", "Комбинаторика", "Теория вероятностей"
        ]
        
        # Создаем дополнительные микротемы
        created_microtopics = []
        existing_microtopics = await MicrotopicRepository.get_by_subject(math_subject.id)
        existing_names = {mt.name for mt in existing_microtopics}
        
        for topic_name in extended_microtopics:
            if topic_name not in existing_names:
                try:
                    microtopic = await MicrotopicRepository.create(
                        name=topic_name,
                        subject_id=math_subject.id
                    )
                    created_microtopics.append(microtopic)
                    print(f"   ✅ Микротема '{topic_name}' создана (номер: {microtopic.number})")
                except ValueError:
                    print(f"   ⚠️ Микротема '{topic_name}' уже существует")
        
        # Получаем все микротемы математики
        all_math_microtopics = await MicrotopicRepository.get_by_subject(math_subject.id)
        print(f"📊 Всего микротем по математике: {len(all_math_microtopics)}")
        
        # Создаем дополнительные уроки и домашние задания
        await create_extended_math_lessons(math_subject, all_math_microtopics)
        
        # Создаем результаты для студента с telegram_id 955518340
        await create_student_results(all_math_microtopics)
        
        print("✅ Расширенные данные по математике созданы!")
        
    except Exception as e:
        print(f"❌ Ошибка при создании расширенных данных: {e}")
        import traceback
        traceback.print_exc()


async def create_extended_math_lessons(math_subject, microtopics):
    """Создание дополнительных уроков по математике"""
    print("📝 Создание дополнительных уроков...")
    
    # Получаем курс ЕНТ
    from database import CourseRepository
    courses = await CourseRepository.get_all()
    ent_course = next((c for c in courses if c.name == "ЕНТ"), None)
    
    if not ent_course:
        print("❌ Курс ЕНТ не найден")
        return
    
    # Группируем микротемы по урокам (по 3-5 микротем на урок)
    lessons_data = [
        {
            "name": "Числовые системы",
            "homework": "Работа с числами",
            "microtopics": [1, 2, 3, 4, 5]  # Натуральные, целые, рациональные, иррациональные, действительные
        },
        {
            "name": "Степени и логарифмы",
            "homework": "Степенные и логарифмические выражения", 
            "microtopics": [7, 8]  # Степени и корни, логарифмы и экспоненты
        },
        {
            "name": "Уравнения",
            "homework": "Решение уравнений",
            "microtopics": [9, 10, 11, 12]  # Линейные, квадратные, кубические, системы
        },
        {
            "name": "Неравенства и модули",
            "homework": "Неравенства",
            "microtopics": [13, 14]  # Неравенства, модуль числа
        },
        {
            "name": "Последовательности",
            "homework": "Прогрессии",
            "microtopics": [15]  # Прогрессии
        },
        {
            "name": "Многочлены и функции",
            "homework": "Исследование функций",
            "microtopics": [16, 17]  # Многочлены, рациональные функции
        },
        {
            "name": "Тригонометрия",
            "homework": "Тригонометрические вычисления",
            "microtopics": [18, 19]  # Тригонометрические и обратные тригонометрические функции
        },
        {
            "name": "Показательные и логарифмические функции",
            "homework": "Экспоненциальные функции",
            "microtopics": [20, 21]  # Показательные и логарифмические функции
        },
        {
            "name": "Пределы и непрерывность",
            "homework": "Анализ функций",
            "microtopics": [22, 23]  # Предел и непрерывность функции
        },
        {
            "name": "Дифференциальное исчисление",
            "homework": "Производные",
            "microtopics": [24, 25, 26]  # Производная, правила дифференцирования, применение
        },
        {
            "name": "Интегральное исчисление",
            "homework": "Интегралы",
            "microtopics": [27, 28, 29, 30]  # Первообразная, неопределенный, определенный, применение
        },
        {
            "name": "Аналитическая геометрия",
            "homework": "Координатная геометрия",
            "microtopics": [31, 32, 33, 34, 35, 36]  # Точка, прямая, окружность, эллипс, парабола, гипербола
        },
        {
            "name": "Планиметрия",
            "homework": "Плоские фигуры",
            "microtopics": [37, 38, 39, 40]  # Треугольники, четырехугольники, многоугольники, площади
        },
        {
            "name": "Стереометрия",
            "homework": "Пространственные фигуры",
            "microtopics": [41]  # Объемы тел
        },
        {
            "name": "Векторная алгебра",
            "homework": "Работа с векторами",
            "microtopics": [42, 43, 44, 45]  # Векторы на плоскости, в пространстве, скалярное, векторное произведение
        },
        {
            "name": "Линейная алгебра",
            "homework": "Матрицы и системы",
            "microtopics": [46, 47, 48]  # Матрицы, определители, системы линейных уравнений
        },
        {
            "name": "Комбинаторика и вероятность",
            "homework": "Статистические расчеты",
            "microtopics": [49, 50]  # Комбинаторика, теория вероятностей
        }
    ]
    
    created_lessons = []
    for lesson_data in lessons_data:
        try:
            # Создаем урок
            lesson = await LessonRepository.create(
                name=lesson_data["name"],
                subject_id=math_subject.id,
                course_id=ent_course.id
            )
            created_lessons.append(lesson)
            print(f"   ✅ Урок '{lesson.name}' создан (ID: {lesson.id})")
            
            # Создаем домашнее задание
            homework = await HomeworkRepository.create(
                name=lesson_data["homework"],
                lesson_id=lesson.id,
                subject_id=math_subject.id
            )
            print(f"   ✅ ДЗ '{homework.name}' создано (ID: {homework.id})")
            
            # Создаем вопросы для каждой микротемы урока
            for microtopic_num in lesson_data["microtopics"]:
                await create_questions_for_microtopic(homework.id, microtopic_num)
                
        except Exception as e:
            print(f"   ❌ Ошибка при создании урока '{lesson_data['name']}': {e}")
    
    return created_lessons


async def create_questions_for_microtopic(homework_id, microtopic_number):
    """Создание вопросов для конкретной микротемы"""
    # Создаем 3-5 вопросов на каждую микротему
    questions_count = random.randint(3, 5)
    
    for i in range(questions_count):
        try:
            question = await QuestionRepository.create(
                homework_id=homework_id,
                text=f"Вопрос {i+1} по микротеме {microtopic_number}. Какой правильный ответ?",
                subject_id=1,  # ID предмета математика
                microtopic_number=microtopic_number
            )
            
            # Создаем 4 варианта ответа
            for j in range(4):
                is_correct = (j == 0)  # Первый ответ всегда правильный
                await AnswerOptionRepository.create(
                    text=f"Вариант {j+1} для вопроса {question.id}",
                    question_id=question.id,
                    is_correct=is_correct
                )
                
        except Exception as e:
            print(f"   ❌ Ошибка при создании вопроса для микротемы {microtopic_number}: {e}")


async def create_student_results(microtopics):
    """Создание результатов для студента с telegram_id 955518340"""
    print("👨‍🎓 Создание результатов для студента...")
    
    # Находим студента
    student = await StudentRepository.get_by_telegram_id(955518340)
    if not student:
        print("❌ Студент с telegram_id 955518340 не найден")
        return
    
    print(f"✅ Найден студент: {student.user.name} (ID: {student.id})")
    
    # Получаем все домашние задания по математике
    from database import SubjectRepository
    subjects = await SubjectRepository.get_all()
    math_subject = next((s for s in subjects if s.name == "Математика"), None)
    
    if not math_subject:
        return
    
    # Получаем уроки математики
    lessons = await LessonRepository.get_by_subject(math_subject.id)
    
    for lesson in lessons:
        homeworks = await HomeworkRepository.get_by_lesson(lesson.id)
        
        for homework in homeworks:
            # Создаем результат домашнего задания
            try:
                # Генерируем случайный результат (60-95% правильных ответов)
                total_questions = len(await QuestionRepository.get_by_homework(homework.id))
                if total_questions == 0:
                    continue
                    
                correct_percentage = random.randint(60, 95)
                correct_answers = int(total_questions * correct_percentage / 100)
                points_earned = correct_answers * 10  # 10 баллов за правильный ответ

                homework_result = await HomeworkResultRepository.create(
                    student_id=student.id,
                    homework_id=homework.id,
                    total_questions=total_questions,
                    correct_answers=correct_answers,
                    points_earned=points_earned
                )
                
                # Создаем результаты для каждого вопроса
                questions = await QuestionRepository.get_by_homework(homework.id)
                for i, question in enumerate(questions):
                    is_correct = i < correct_answers  # Первые N вопросов правильные
                    
                    await QuestionResultRepository.create(
                        homework_result_id=homework_result.id,
                        question_id=question.id,
                        is_correct=is_correct,
                        microtopic_number=question.microtopic_number
                    )
                
                print(f"   ✅ Результат ДЗ '{homework.name}': {correct_answers}/{total_questions} ({correct_percentage}%)")
                
            except Exception as e:
                print(f"   ❌ Ошибка при создании результата для ДЗ {homework.id}: {e}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(create_extended_math_data())
